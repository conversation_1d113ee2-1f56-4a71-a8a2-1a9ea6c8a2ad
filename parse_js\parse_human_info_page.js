/**
 * LinkedIn人物详情页解析 - JavaScript版本
 * 对应Python的link/parse_human_info_page.py
 */

// 导入依赖（在浏览器环境中通过script标签引入）
// const { ParseHumanInfo, WorkExperience, EducationalExperience, LanguageAbility } = require('./parse_models.js');
// const { customUnescape, getElementsByXPath, getTextByXPath, safeJsonParse, extractLinkedInId, cleanText } = require('./parse_utils.js');

/**
 * 未登录人物详情解析类
 */
class NotLoginHumanInfoParse {
    constructor() {
        // XPath选择器
        this.humanNameXpath = './/h1[contains(@class, "text-heading-xlarge")]//text()';
        this.linkedinUsernameXpath = './/link[@rel="canonical"]/@href';
        this.humanProfilesXpath = './/div[contains(@class, "text-body-medium")]//text()';
        this.humanAvatarXpath = './/img[contains(@class, "profile-photo-edit")]/@src';
        this.jobTitleXpath = './/div[contains(@class, "text-body-medium")]//text()';
        this.jobCompanyXpath = './/div[contains(@class, "inline-show-more-text")]//text()';
        this.jobLocationXpath = './/span[contains(@class, "text-body-small")]//text()';
        this.connectionsCountXpath = './/span[contains(@class, "t-bold")]//text()';
        
        // 工作经历相关XPath
        this.workExperienceXpath = './/section[contains(@id, "experience")]//ul/li';
        this.educationXpath = './/section[contains(@id, "education")]//ul/li';
        this.languageXpath = './/section[contains(@id, "languages")]//ul/li';
        this.skillsXpath = './/section[contains(@id, "skills")]//ul/li';
    }

    /**
     * 处理数据提取
     * @param {Element} htmlObj - HTML元素
     * @param {string} xpath - XPath表达式
     * @returns {Array} 提取的数据数组
     */
    processingData(htmlObj, xpath) {
        const utils = window.ParseUtils || { getElementsByXPath, getTextByXPath, cleanText };
        const elements = utils.getElementsByXPath(xpath, htmlObj);
        return elements.map(el => utils.cleanText(el.textContent || el.nodeValue || ''));
    }

    /**
     * 解析人物基本信息
     * @param {Element} htmlObj - HTML元素
     * @returns {Object} 人物基本信息
     */
    parseHumanBaseInfo(htmlObj) {
        const utils = window.ParseUtils || { getElementsByXPath, safeJsonParse };
        const humanData = { country_code: '', human_avatar: '' };
        
        // 从JSON-LD中提取信息
        const jsonLdElements = utils.getElementsByXPath('.//script[@type="application/ld+json"]', htmlObj);
        
        for (const element of jsonLdElements) {
            const jsonStr = element.textContent || '';
            const linkerJsonData = utils.safeJsonParse(jsonStr);
            
            if (linkerJsonData && typeof linkerJsonData === 'object') {
                const graph = linkerJsonData['@graph'] || [];
                for (const data of graph) {
                    if (data['@type'] === 'Person') {
                        humanData.country_code = data.address?.addressCountry || '';
                        humanData.human_avatar = data.image?.contentUrl || '';
                        break;
                    }
                }
            }
        }
        
        // 提取基本信息
        const humanName = this.processingData(htmlObj, this.humanNameXpath);
        const humanId = this.processingData(htmlObj, this.linkedinUsernameXpath);
        const humanProfiles = this.processingData(htmlObj, this.humanProfilesXpath);
        const jobTitle = this.processingData(htmlObj, this.jobTitleXpath);
        const jobLocation = this.processingData(htmlObj, this.jobLocationXpath);
        const connectionsCount = this.processingData(htmlObj, this.connectionsCountXpath);
        
        return {
            ...humanData,
            human_name: humanName[0] || '',
            linkedin_username: humanId[0] ? extractLinkedInId(humanId[0]) : '',
            linkedin_url: humanId[0] || '',
            human_profiles: humanProfiles.join(' '),
            job_title: jobTitle[0] || '',
            job_location: jobLocation[0] || '',
            connections_count: connectionsCount[0] || ''
        };
    }

    /**
     * 解析工作经历
     * @param {Element} htmlObj - HTML元素
     * @returns {Array} 工作经历数组
     */
    parseWorkExperience(htmlObj) {
        const utils = window.ParseUtils || { getElementsByXPath, cleanText };
        const workExperience = [];
        const workElements = utils.getElementsByXPath(this.workExperienceXpath, htmlObj);
        
        workElements.forEach(element => {
            const titleElement = utils.getElementsByXPath('.//div[contains(@class, "mr1")]//text()', element);
            const companyElement = utils.getElementsByXPath('.//span[contains(@class, "t-14")]//text()', element);
            const durationElement = utils.getElementsByXPath('.//span[contains(@class, "t-12")]//text()', element);
            const locationElement = utils.getElementsByXPath('.//span[contains(@class, "t-12")]//text()', element);
            const descriptionElement = utils.getElementsByXPath('.//div[contains(@class, "inline-show-more-text")]//text()', element);
            const logoElement = utils.getElementsByXPath('.//img/@src', element);
            
            const workData = new (window.ParseModels?.WorkExperience || WorkExperience)({
                job_title: titleElement.length > 0 ? utils.cleanText(titleElement[0].textContent || '') : '',
                company_name: companyElement.length > 0 ? utils.cleanText(companyElement[0].textContent || '') : '',
                duration: durationElement.length > 0 ? utils.cleanText(durationElement[0].textContent || '') : '',
                location: locationElement.length > 1 ? utils.cleanText(locationElement[1].textContent || '') : '',
                description: descriptionElement.length > 0 ? utils.cleanText(descriptionElement[0].textContent || '') : '',
                company_logo: logoElement.length > 0 ? logoElement[0].nodeValue || '' : ''
            });
            
            if (workData.job_title || workData.company_name) {
                workExperience.push(workData);
            }
        });
        
        return workExperience;
    }

    /**
     * 解析教育经历
     * @param {Element} htmlObj - HTML元素
     * @returns {Array} 教育经历数组
     */
    parseEducationalExperience(htmlObj) {
        const utils = window.ParseUtils || { getElementsByXPath, cleanText };
        const educationalExperience = [];
        const eduElements = utils.getElementsByXPath(this.educationXpath, htmlObj);
        
        eduElements.forEach(element => {
            const schoolElement = utils.getElementsByXPath('.//div[contains(@class, "mr1")]//text()', element);
            const degreeElement = utils.getElementsByXPath('.//span[contains(@class, "t-14")]//text()', element);
            const durationElement = utils.getElementsByXPath('.//span[contains(@class, "t-12")]//text()', element);
            const descriptionElement = utils.getElementsByXPath('.//div[contains(@class, "inline-show-more-text")]//text()', element);
            const logoElement = utils.getElementsByXPath('.//img/@src', element);
            
            const eduData = new (window.ParseModels?.EducationalExperience || EducationalExperience)({
                school_name: schoolElement.length > 0 ? utils.cleanText(schoolElement[0].textContent || '') : '',
                degree: degreeElement.length > 0 ? utils.cleanText(degreeElement[0].textContent || '') : '',
                duration: durationElement.length > 0 ? utils.cleanText(durationElement[0].textContent || '') : '',
                description: descriptionElement.length > 0 ? utils.cleanText(descriptionElement[0].textContent || '') : '',
                school_logo: logoElement.length > 0 ? logoElement[0].nodeValue || '' : ''
            });
            
            if (eduData.school_name || eduData.degree) {
                educationalExperience.push(eduData);
            }
        });
        
        return educationalExperience;
    }

    /**
     * 解析语言能力
     * @param {Element} htmlObj - HTML元素
     * @returns {Array} 语言能力数组
     */
    parseLanguageAbility(htmlObj) {
        const utils = window.ParseUtils || { getElementsByXPath, cleanText };
        const languageAbility = [];
        const langElements = utils.getElementsByXPath(this.languageXpath, htmlObj);
        
        langElements.forEach(element => {
            const languageElement = utils.getElementsByXPath('.//div[contains(@class, "mr1")]//text()', element);
            const proficiencyElement = utils.getElementsByXPath('.//span[contains(@class, "t-12")]//text()', element);
            
            const langData = new (window.ParseModels?.LanguageAbility || LanguageAbility)({
                language: languageElement.length > 0 ? utils.cleanText(languageElement[0].textContent || '') : '',
                proficiency: proficiencyElement.length > 0 ? utils.cleanText(proficiencyElement[0].textContent || '') : ''
            });
            
            if (langData.language) {
                languageAbility.push(langData);
            }
        });
        
        return languageAbility;
    }

    /**
     * 解析技能
     * @param {Element} htmlObj - HTML元素
     * @returns {Array} 技能数组
     */
    parseSkills(htmlObj) {
        const utils = window.ParseUtils || { getElementsByXPath, cleanText };
        const skills = [];
        const skillElements = utils.getElementsByXPath(this.skillsXpath, htmlObj);
        
        skillElements.forEach(element => {
            const skillText = utils.cleanText(element.textContent || '');
            if (skillText) {
                skills.push(skillText);
            }
        });
        
        return skills;
    }

    /**
     * 主解析方法
     * @param {string} responseStr - HTML字符串
     * @returns {ParseHumanInfo} 解析后的人物信息
     */
    parseData(responseStr) {
        const utils = window.ParseUtils || { customUnescape };
        const htmlStr = utils.customUnescape(responseStr);
        const parser = new DOMParser();
        const htmlObj = parser.parseFromString(htmlStr, 'text/html');

        // 解析基本信息
        const humanData = this.parseHumanBaseInfo(htmlObj);

        // 解析工作经历
        humanData.work_experience = this.parseWorkExperience(htmlObj);

        // 解析教育经历
        humanData.educational_experience = this.parseEducationalExperience(htmlObj);

        // 解析语言能力
        humanData.language_ability = this.parseLanguageAbility(htmlObj);

        // 解析技能
        humanData.skills = this.parseSkills(htmlObj);

        return new (window.ParseModels?.ParseHumanInfo || ParseHumanInfo)(humanData);
    }
}

/**
 * 登录状态下的人物详情解析类
 */
class LoginHumanParse {
    constructor() {
        this.jsonDataXpath = './/code[contains(@id,"bpr-guid-") and not(contains(@id,"datalet"))]//text()';
        this.humanNamePat = /\/in\/(.*?)\/overlay\//;
    }

    /**
     * 主解析方法
     * @param {string} htmlStr - HTML字符串
     * @returns {Object} 解析后的人物信息
     */
    parseData(htmlStr) {
        const utils = window.ParseUtils || { customUnescape };
        const htmlStr1 = utils.customUnescape(htmlStr);

        // 提取人物ID
        const humanNameMatch = this.humanNamePat.exec(htmlStr1);
        const humanName = humanNameMatch ? humanNameMatch[1] : '';

        const dataObj = {
            html_str: htmlStr1,
            human_name: humanName
        };

        return dataObj;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { NotLoginHumanInfoParse, LoginHumanParse };
} else {
    window.NotLoginHumanInfoParse = NotLoginHumanInfoParse;
    window.LoginHumanParse = LoginHumanParse;
}
