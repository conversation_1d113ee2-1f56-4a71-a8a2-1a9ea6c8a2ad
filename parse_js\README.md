# LinkedIn页面解析器 - JavaScript版本

这是一个用于解析LinkedIn页面的JavaScript库，可以集成到Chrome插件中使用。它能够解析LinkedIn的搜索页面、人物详情页和公司详情页，提取结构化数据。

## 文件结构

- `index.js` - 主入口文件，提供解析API
- `parse.js` - 解析入口，根据URL类型选择合适的解析方法
- `parse_utils.js` - 工具函数
- `parse_models.js` - 数据模型定义
- `parse_search_page.js` - 搜索页面解析
- `parse_human_info_page.js` - 人物详情页解析
- `parse_company_info_page.js` - 公司详情页解析

## 在Chrome插件中使用

### 1. 在manifest.json中声明

```json
{
  "content_scripts": [
    {
      "matches": ["*://*.linkedin.com/*"],
      "js": [
        "parse_js/parse_utils.js",
        "parse_js/parse_models.js",
        "parse_js/parse_search_page.js",
        "parse_js/parse_human_info_page.js",
        "parse_js/parse_company_info_page.js",
        "parse_js/parse.js",
        "parse_js/index.js"
      ]
    }
  ]
}
```

### 2. 在插件中调用

```javascript
// 获取当前页面的HTML
const html = document.documentElement.outerHTML;
const url = window.location.href;

// 调用解析函数
const result = window.LinkedInParser.parse({
  url: url,
  html: html,
  isZip: false
});

// 处理解析结果
console.log('解析结果:', result);
```

### 3. 发送解析结果到服务器

```javascript
async function sendToServer(data) {
  try {
    const response = await fetch('https://your-server.com/api/linkedin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    const result = await response.json();
    console.log('服务器响应:', result);
    return result;
  } catch (error) {
    console.error('发送数据失败:', error);
    throw error;
  }
}

// 解析并发送
async function parseAndSend() {
  const html = document.documentElement.outerHTML;
  const url = window.location.href;
  
  const parseResult = await window.LinkedInParser.parse({
    url: url,
    html: html
  });
  
  return sendToServer(parseResult);
}
```

## API参考

### 主要函数

#### `LinkedInParser.parse(options)`

解析LinkedIn页面数据。

**参数:**
- `options` (Object): 解析选项
  - `url` (string): 页面URL
  - `html` (string): 页面HTML内容
  - `isZip` (boolean, 可选): 是否为压缩数据，默认为false
  - `type` (string, 可选): 强制指定解析类型，可选值: 'search', 'person', 'company'

**返回值:**
- Promise<Object>: 解析结果
  - `people`: 人物数据数组
  - `companies`: 公司数据数组
  - `meta`: 元数据

#### `LinkedInParser.parseMultiple(pageList)`

批量解析多个页面。

**参数:**
- `pageList` (Array): 页面列表，每个元素都是一个包含url和html的对象

**返回值:**
- Promise<Array>: 解析结果列表

#### `LinkedInParser.getStatus()`

获取解析器状态信息。

**返回值:**
- Object: 状态信息

#### `LinkedInParser.clearCache()`

清理解析器缓存。

## 支持的页面类型

1. 搜索页面
   - 综合搜索页 (`search/results/all`)
   - 人物搜索页 (`search/results/people`)
   - 公司搜索页 (`search/results/companies`)

2. 详情页面
   - 人物详情页 (`linkedin.com/in/...`)
   - 公司详情页 (`linkedin.com/company/...`)
   - 人物联系方式页 (`/overlay/contact-info/`)

3. API数据
   - GraphQL API数据 (`linkedin.com/voyager/api/graphql`)
   - 登录用户的公司信息 (`/voyager/api/me`)

## 数据模型

### 人物数据模型

```javascript
{
  linkedin_id: '',           // 领英人物ID
  linkedin_username: '',     // 领英人物链接ID
  linkedin_url: '',          // 领英人物链接
  human_name: '',            // 领英人物名称
  human_avatar: '',          // 领英人物头像
  human_profiles: '',        // 领英人物简介
  job_title: '',             // 领英人物职位
  job_company_name: '',      // 领英人物公司名称
  job_location: '',          // 领英人物工作地点
  country_code: '',          // 国家代码
  work_experience: [],       // 工作经历
  educational_experience: [], // 教育经历
  language_ability: [],      // 语言能力
  skills: [],                // 技能
  connections_count: ''      // 连接数量
}
```

### 公司数据模型

```javascript
{
  job_company_id: '',        // 领英公司链接ID
  job_company_linkedin_id: '', // 领英公司ID
  job_company_linkedin_url: '', // 领英公司链接
  job_company_name: '',      // 领英公司名称
  company_logo: '',          // 领英公司logo
  company_profiles: '',      // 领英公司简介
  company_website: '',       // 领英公司网站
  company_industry: '',      // 领英公司行业
  company_size: '',          // 领英公司规模
  company_location: '',      // 领英公司位置
  company_founded: '',       // 领英公司成立时间
  company_specialties: '',   // 领英公司专长
  followers: '',             // 关注者数量
  employees: [],             // 员工信息
  overview: {},              // 概览信息
  job_url: ''                // 职位搜索链接
}
```

## 注意事项

1. 确保按正确顺序加载所有JS文件，依赖关系为：
   - `parse_utils.js` → `parse_models.js` → 其他解析文件 → `parse.js` → `index.js`

2. 解析结果可能因LinkedIn页面结构变化而失效，需要定期更新解析逻辑。

3. 使用此解析器时，请遵守LinkedIn的使用条款和隐私政策。
