/**
 * LinkedIn解析器主入口文件
 * 用于Chrome插件集成
 */

// 加载所有依赖模块
// 注意：在Chrome插件中，这些文件需要通过script标签按顺序加载

/**
 * 初始化LinkedIn解析器
 * 确保所有依赖都已加载
 */
function initLinkedInParser() {
    // 检查依赖是否已加载
    const dependencies = [
        'ParseUtils',
        'ParseModels', 
        'SearchAllParse',
        'NotLoginHumanInfoParse',
        'LoginHumanParse',
        'NotLoginCompanyParse',
        'LoginCompanyParse',
        'LinkParser'
    ];
    
    const missing = dependencies.filter(dep => !window[dep]);
    
    if (missing.length > 0) {
        console.error('缺少依赖模块:', missing);
        return false;
    }
    
    console.log('LinkedIn解析器初始化成功');
    return true;
}

/**
 * 主解析接口 - 供Chrome插件调用
 * @param {Object} options - 解析选项
 * @param {string} options.url - 页面URL
 * @param {string} options.html - 页面HTML内容
 * @param {boolean} options.isZip - 是否为压缩数据
 * @param {string} options.type - 强制指定解析类型（可选）
 * @returns {Promise<Object>} 解析结果
 */
async function parseLinkedInData(options) {
    try {
        if (!initLinkedInParser()) {
            throw new Error('解析器初始化失败');
        }
        
        const { url, html, isZip = false, type } = options;
        
        if (!url || !html) {
            throw new Error('缺少必要参数：url 和 html');
        }
        
        // 获取解析器实例
        const parser = window.getParser();
        
        let result;
        
        if (type) {
            // 强制指定解析类型
            switch (type) {
                case 'search':
                    result = parser.parseSearchPage(html, 'html');
                    break;
                case 'person':
                    result = { people: [parser.parsePersonPage(html, false)], companies: [] };
                    break;
                case 'company':
                    result = { people: [], companies: [parser.parseCompanyPage(html, false)] };
                    break;
                default:
                    result = parser.parseByUrl(url, html, isZip);
            }
        } else {
            // 自动识别页面类型
            result = parser.parseByUrl(url, html, isZip);
        }
        
        // 添加元数据
        result.meta = {
            url: url,
            parseTime: new Date().toISOString(),
            peopleCount: result.people ? result.people.length : 0,
            companiesCount: result.companies ? result.companies.length : 0
        };
        
        return result;
        
    } catch (error) {
        console.error('解析LinkedIn数据时发生错误:', error);
        return {
            people: [],
            companies: [],
            error: error.message,
            meta: {
                url: options.url || '',
                parseTime: new Date().toISOString(),
                peopleCount: 0,
                companiesCount: 0
            }
        };
    }
}

/**
 * 批量解析多个页面
 * @param {Array} pageList - 页面列表
 * @returns {Promise<Array>} 解析结果列表
 */
async function parseMultiplePages(pageList) {
    const results = [];
    
    for (const page of pageList) {
        try {
            const result = await parseLinkedInData(page);
            results.push(result);
        } catch (error) {
            console.error('解析页面失败:', page.url, error);
            results.push({
                people: [],
                companies: [],
                error: error.message,
                meta: {
                    url: page.url || '',
                    parseTime: new Date().toISOString(),
                    peopleCount: 0,
                    companiesCount: 0
                }
            });
        }
    }
    
    return results;
}

/**
 * 获取解析器状态信息
 * @returns {Object} 状态信息
 */
function getParserStatus() {
    const dependencies = [
        'ParseUtils',
        'ParseModels', 
        'SearchAllParse',
        'NotLoginHumanInfoParse',
        'LoginHumanParse',
        'NotLoginCompanyParse',
        'LoginCompanyParse',
        'LinkParser'
    ];
    
    const status = {
        initialized: true,
        dependencies: {},
        version: '1.0.0',
        timestamp: new Date().toISOString()
    };
    
    dependencies.forEach(dep => {
        status.dependencies[dep] = !!window[dep];
        if (!window[dep]) {
            status.initialized = false;
        }
    });
    
    return status;
}

/**
 * 清理解析器缓存
 */
function clearParserCache() {
    // 重置全局解析器实例
    if (window.globalParser) {
        window.globalParser = null;
    }
    console.log('解析器缓存已清理');
}

// 导出到全局
if (typeof window !== 'undefined') {
    window.LinkedInParser = {
        init: initLinkedInParser,
        parse: parseLinkedInData,
        parseMultiple: parseMultiplePages,
        getStatus: getParserStatus,
        clearCache: clearParserCache,
        version: '1.0.0'
    };
    
    // 自动初始化
    document.addEventListener('DOMContentLoaded', () => {
        initLinkedInParser();
    });
}

// Node.js环境导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initLinkedInParser,
        parseLinkedInData,
        parseMultiplePages,
        getParserStatus,
        clearParserCache
    };
}
