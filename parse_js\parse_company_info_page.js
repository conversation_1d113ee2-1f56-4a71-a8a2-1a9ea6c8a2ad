/**
 * LinkedIn公司详情页解析 - JavaScript版本
 * 对应Python的link/parse_company_info_page.py
 */

// 导入依赖（在浏览器环境中通过script标签引入）
// const { ParseCompanyInfo, EmployeeInfo } = require('./parse_models.js');
// const { customUnescape, getElementsByXPath, getTextByXPath, safeJsonParse, extractLinkedInId, cleanText } = require('./parse_utils.js');

/**
 * 未登录公司详情解析类
 */
class NotLoginCompanyParse {
    constructor() {
        this.jsonData = './/script[@type="application/ld+json"]//text()';
        this.overviewXpath = './/dl[@class="mt-6"]/div';
        this.jobUrlXpath = './/a[@data-tracking-control-name="nav_type_jobs"]/@href';
        this.humanXpath = './/section[@data-test-id="employees-at"]/div[@class="core-section-container__content break-words"]/ul/li';
        this.replaceDict = {
            '\n': '',
            '\t': '<'
        };
    }

    /**
     * 主解析方法
     * @param {string} htmlStr - HTML字符串
     * @returns {ParseCompanyInfo} 解析后的公司信息
     */
    parseData(htmlStr) {
        const utils = window.ParseUtils || { customUnescape, getElementsByXPath, safeJsonParse, cleanText };
        const parser = new DOMParser();
        const html = parser.parseFromString(htmlStr, 'text/html');
        
        // 解析JSON-LD数据
        let jsonData = {};
        const orgDataElements = utils.getElementsByXPath(this.jsonData, html);
        
        try {
            if (orgDataElements.length > 0) {
                const orgDataStr = orgDataElements[0].textContent || '';
                const orgData = utils.safeJsonParse(orgDataStr);
                
                if (orgData && orgData['@graph']) {
                    const mainData = orgData['@graph'][orgData['@graph'].length - 1] || {};
                    const addressData = orgData['@graph'][9]?.address || {};
                    jsonData = { ...mainData, ...addressData };
                }
            }
        } catch (error) {
            console.warn('JSON-LD解析失败:', error);
            jsonData = {};
        }
        
        // 解析概览信息
        const overview = {};
        const items = utils.getElementsByXPath(this.overviewXpath, html);
        
        items.forEach((item, index) => {
            const keyXpath = `${this.overviewXpath}[${index + 1}]/dt[1]//text()`;
            const valueXpath = `${this.overviewXpath}[${index + 1}]/dd[1]//text()`;
            
            const keyElements = utils.getElementsByXPath(keyXpath, html);
            const valueElements = utils.getElementsByXPath(valueXpath, html);
            
            const key = keyElements.length > 0 ? 
                utils.customUnescape(keyElements[0].textContent || '', this.replaceDict).trim() : '';
            const value = valueElements.length > 0 ? 
                utils.customUnescape(valueElements[0].textContent || '', this.replaceDict).trim() : '';
            
            if (key && value) {
                overview[key] = value;
            }
        });
        
        jsonData.overview = overview;
        
        // 解析职位搜索链接
        const jobUrlElements = utils.getElementsByXPath(this.jobUrlXpath, html);
        const jobUrl = jobUrlElements.length > 0 ? jobUrlElements[0].nodeValue || '' : '';
        jsonData.job_url = jobUrl;
        
        // 解析员工信息
        const humanList = [];
        const humanItems = utils.getElementsByXPath(this.humanXpath, html);
        
        humanItems.forEach((humanItem, humanIndex) => {
            const nameXpath = `${this.humanXpath}[${humanIndex + 1}]//h3//text()`;
            const titleXpath = `${this.humanXpath}[${humanIndex + 1}]//p//text()`;
            const linkXpath = `${this.humanXpath}[${humanIndex + 1}]//a/@href`;
            const avatarXpath = `${this.humanXpath}[${humanIndex + 1}]//img/@src`;
            
            const nameElements = utils.getElementsByXPath(nameXpath, html);
            const titleElements = utils.getElementsByXPath(titleXpath, html);
            const linkElements = utils.getElementsByXPath(linkXpath, html);
            const avatarElements = utils.getElementsByXPath(avatarXpath, html);
            
            const name = nameElements.length > 0 ? utils.cleanText(nameElements[0].textContent || '') : '';
            const title = titleElements.length > 0 ? utils.cleanText(titleElements[0].textContent || '') : '';
            const linkedinUrl = linkElements.length > 0 ? linkElements[0].nodeValue || '' : '';
            const avatar = avatarElements.length > 0 ? avatarElements[0].nodeValue || '' : '';
            
            if (name) {
                const employeeData = new (window.ParseModels?.EmployeeInfo || EmployeeInfo)({
                    name: name,
                    title: title,
                    linkedin_url: linkedinUrl,
                    avatar: avatar
                });
                humanList.push(employeeData);
            }
        });
        
        jsonData.employees = humanList;
        
        // 构建公司信息对象
        const companyData = new (window.ParseModels?.ParseCompanyInfo || ParseCompanyInfo)({
            job_company_name: jsonData.name || '',
            job_company_linkedin_url: jsonData.url || '',
            job_company_id: jsonData.url ? utils.extractLinkedInId(jsonData.url) : '',
            company_logo: jsonData.logo?.url || '',
            company_profiles: jsonData.description || '',
            company_website: jsonData.sameAs || '',
            company_industry: jsonData.industry || '',
            company_location: jsonData.addressLocality || jsonData.addressRegion || '',
            company_founded: jsonData.foundingDate || '',
            followers: jsonData.numberOfEmployees || '',
            overview: overview,
            job_url: jobUrl,
            employees: humanList
        });
        
        return companyData;
    }
}

/**
 * 登录状态下的公司详情解析类
 */
class LoginCompanyParse {
    constructor() {
        this.jsonDataXpath = './/code[contains(@id,"bpr-guid-") and not(contains(@id,"datalet"))]//text()';
        this.companyNameXpath = './/a[@aria-current="page"]/@href';
        this.companyNamePat = /\/company\/(.*?)\//;
    }

    /**
     * 主解析方法
     * @param {string} htmlStr - HTML字符串
     * @returns {Object} 解析后的公司信息
     */
    parseData(htmlStr) {
        const utils = window.ParseUtils || { getElementsByXPath };
        const parser = new DOMParser();
        const htmlObj = parser.parseFromString(htmlStr, 'text/html');
        
        // 提取公司ID
        const companyNameElements = utils.getElementsByXPath(this.companyNameXpath, htmlObj);
        const companyNameUrl = companyNameElements.length > 0 ? companyNameElements[0].nodeValue || '' : '';
        
        const companyNameMatch = this.companyNamePat.exec(companyNameUrl);
        const companyName = companyNameMatch ? companyNameMatch[1] : '';
        
        const dataObj = {
            html_str: htmlStr,
            company_name: companyName
        };
        
        return dataObj;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { NotLoginCompanyParse, LoginCompanyParse };
} else {
    window.NotLoginCompanyParse = NotLoginCompanyParse;
    window.LoginCompanyParse = LoginCompanyParse;
}
