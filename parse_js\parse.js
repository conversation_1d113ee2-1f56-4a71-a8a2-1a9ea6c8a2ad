/**
 * LinkedIn解析入口 - JavaScript版本
 * 对应Python的link/parse.py
 */

// 导入依赖（在浏览器环境中通过script标签引入）
// const { SearchAllParse } = require('./parse_search_page.js');
// const { NotLoginHumanInfoParse, LoginHumanParse } = require('./parse_human_info_page.js');
// const { NotLoginCompanyParse, LoginCompanyParse } = require('./parse_company_info_page.js');

/**
 * LinkedIn解析器主类
 */
class LinkParser {
    constructor() {
        // 综合搜索页解析
        this.searchAllParseObj = new (window.SearchAllParse || SearchAllParse)();
        
        // 未登录人物详情解析
        this.notLoginHumanInfoParseObj = new (window.NotLoginHumanInfoParse || NotLoginHumanInfoParse)();
        
        // 登录人物页详情解析
        this.loginHumanInfoParseObj = new (window.LoginHumanParse || LoginHumanParse)();
        
        // 未登录页公司详情解析
        this.notLoginCompanyParseObj = new (window.NotLoginCompanyParse || NotLoginCompanyParse)();
        
        // 登录公司详情解析
        this.loginCompanyParseObj = new (window.LoginCompanyParse || LoginCompanyParse)();
    }

    /**
     * 根据URL类型选择合适的解析方法
     * @param {string} linkUrl - LinkedIn页面URL
     * @param {string} htmlStr - 页面HTML内容
     * @param {boolean} isZip - 是否为压缩数据
     * @returns {Object} 解析结果 {people: [], companies: []}
     */
    parseByUrl(linkUrl, htmlStr, isZip = false) {
        if (!linkUrl || !htmlStr) {
            return { people: [], companies: [] };
        }

        // 如果是压缩数据，需要先解压（这里假设有解压函数）
        if (isZip) {
            // htmlStr = decompressFromEncodedURIComponent(htmlStr);
            console.warn('压缩数据解压功能需要实现');
        }

        try {
            // 判断页面类型并调用相应的解析方法
            if (linkUrl.includes('search/results/all')) {
                // 综合搜索页
                console.log('解析综合搜索页:', linkUrl.substring(0, 100));
                return this.searchAllParseObj.searchAllPageParser(htmlStr, 'html_all');
                
            } else if (linkUrl.includes('search/results/people')) {
                // 人物搜索页
                console.log('解析人物搜索页:', linkUrl.substring(0, 100));
                return this.searchAllParseObj.searchAllPageParser(htmlStr, 'html_people');
                
            } else if (linkUrl.includes('search/results/companies')) {
                // 公司搜索页
                console.log('解析公司搜索页:', linkUrl.substring(0, 100));
                return this.searchAllParseObj.searchAllPageParser(htmlStr, 'html_company');
                
            } else if (linkUrl.includes('linkedin.com/company')) {
                // 公司详情页
                console.log('解析公司详情页:', linkUrl.substring(0, 100));
                const companyData = this.notLoginCompanyParseObj.parseData(htmlStr);
                return { people: [], companies: [companyData] };
                
            } else if (linkUrl.includes('/overlay/contact-info/')) {
                // 人物联系方式页
                console.log('解析人物联系方式页:', linkUrl.substring(0, 100));
                const personData = this.loginHumanInfoParseObj.parseData(htmlStr);
                return { people: [personData], companies: [] };
                
            } else if (linkUrl.includes('linkedin.com/in')) {
                // 人物详情页
                console.log('解析人物详情页:', linkUrl.substring(0, 100));
                const personData = this.notLoginHumanInfoParseObj.parseData(htmlStr);
                return { people: [personData], companies: [] };
                
            } else if (linkUrl.includes('linkedin.com/voyager/api/graphql')) {
                // GraphQL API数据
                console.log('解析GraphQL API数据:', linkUrl.substring(0, 100));
                return this.searchAllParseObj.searchAllPageParser(htmlStr, 'json');
                
            } else if (linkUrl.includes('/voyager/api/me')) {
                // 登录用户的公司信息
                console.log('解析登录用户公司信息:', linkUrl.substring(0, 100));
                const companyData = this.loginCompanyParseObj.parseData(htmlStr);
                return { people: [], companies: [companyData] };
                
            } else {
                // 默认尝试搜索页解析
                console.log('尝试默认搜索页解析:', linkUrl.substring(0, 100));
                return this.searchAllParseObj.searchAllPageParser(htmlStr, 'html');
            }
            
        } catch (error) {
            console.error('解析过程中发生错误:', error);
            return { people: [], companies: [] };
        }
    }

    /**
     * 解析搜索页面
     * @param {string} htmlStr - HTML内容
     * @param {string} fromType - 解析类型
     * @returns {Object} 解析结果
     */
    parseSearchPage(htmlStr, fromType = 'html') {
        return this.searchAllParseObj.searchAllPageParser(htmlStr, fromType);
    }

    /**
     * 解析人物详情页
     * @param {string} htmlStr - HTML内容
     * @param {boolean} isLogin - 是否为登录状态
     * @returns {Object} 解析结果
     */
    parsePersonPage(htmlStr, isLogin = false) {
        if (isLogin) {
            return this.loginHumanInfoParseObj.parseData(htmlStr);
        } else {
            return this.notLoginHumanInfoParseObj.parseData(htmlStr);
        }
    }

    /**
     * 解析公司详情页
     * @param {string} htmlStr - HTML内容
     * @param {boolean} isLogin - 是否为登录状态
     * @returns {Object} 解析结果
     */
    parseCompanyPage(htmlStr, isLogin = false) {
        if (isLogin) {
            return this.loginCompanyParseObj.parseData(htmlStr);
        } else {
            return this.notLoginCompanyParseObj.parseData(htmlStr);
        }
    }
}

/**
 * 全局解析器实例
 */
let globalParser = null;

/**
 * 获取全局解析器实例
 * @returns {LinkParser} 解析器实例
 */
function getParser() {
    if (!globalParser) {
        globalParser = new LinkParser();
    }
    return globalParser;
}

/**
 * 主解析函数 - 供Chrome插件调用
 * @param {string} linkUrl - LinkedIn页面URL
 * @param {string} htmlStr - 页面HTML内容
 * @param {boolean} isZip - 是否为压缩数据
 * @returns {Object} 解析结果
 */
function parseLinkedInPage(linkUrl, htmlStr, isZip = false) {
    const parser = getParser();
    return parser.parseByUrl(linkUrl, htmlStr, isZip);
}

/**
 * 测试函数
 */
function testUnit() {
    // 这里可以添加测试代码
    console.log('LinkedIn解析器已加载');
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        LinkParser,
        getParser,
        parseLinkedInPage,
        testUnit
    };
} else {
    // 浏览器环境
    window.LinkParser = LinkParser;
    window.getParser = getParser;
    window.parseLinkedInPage = parseLinkedInPage;
    window.testUnit = testUnit;
    
    // 自动初始化
    if (typeof window !== 'undefined') {
        console.log('LinkedIn解析器已在浏览器环境中加载');
    }
}
