/**
 * LinkedIn页面HTML复制助手
 * 在LinkedIn页面的控制台中运行此脚本，可以方便地复制页面数据
 */

(function() {
    'use strict';
    
    // 创建复制助手UI
    function createCopyHelper() {
        // 检查是否已经存在
        if (document.getElementById('linkedin-copy-helper')) {
            return;
        }
        
        // 创建主容器
        const helper = document.createElement('div');
        helper.id = 'linkedin-copy-helper';
        helper.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background: white;
            border: 2px solid #0077b5;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
        `;
        
        // 创建标题栏
        const header = document.createElement('div');
        header.style.cssText = `
            background: #0077b5;
            color: white;
            padding: 10px 15px;
            border-radius: 6px 6px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
        `;
        header.innerHTML = `
            <span>🔧 LinkedIn复制助手</span>
            <span id="close-helper" style="cursor: pointer; font-size: 18px;">&times;</span>
        `;
        
        // 创建内容区域
        const content = document.createElement('div');
        content.style.cssText = `
            padding: 15px;
        `;
        
        content.innerHTML = `
            <div style="margin-bottom: 15px;">
                <strong>当前页面:</strong><br>
                <small style="color: #666; word-break: break-all;">${window.location.href}</small>
            </div>
            
            <div style="margin-bottom: 10px;">
                <button id="copy-html" style="
                    width: 100%;
                    padding: 8px;
                    background: #0077b5;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    margin-bottom: 8px;
                ">📋 复制完整HTML</button>
                
                <button id="copy-data" style="
                    width: 100%;
                    padding: 8px;
                    background: #28a745;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    margin-bottom: 8px;
                ">📦 复制页面数据(JSON)</button>
                
                <button id="copy-clean" style="
                    width: 100%;
                    padding: 8px;
                    background: #6c757d;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                ">🧹 复制清理后HTML</button>
            </div>
            
            <div id="copy-status" style="
                padding: 8px;
                border-radius: 4px;
                text-align: center;
                font-size: 12px;
                display: none;
            "></div>
            
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                💡 复制后可以粘贴到debug.html中进行测试
            </div>
        `;
        
        helper.appendChild(header);
        helper.appendChild(content);
        document.body.appendChild(helper);
        
        // 添加事件监听器
        setupEventListeners(helper);
        
        console.log('✅ LinkedIn复制助手已加载');
    }
    
    // 设置事件监听器
    function setupEventListeners(helper) {
        // 关闭按钮
        helper.querySelector('#close-helper').addEventListener('click', () => {
            helper.remove();
        });
        
        // 复制完整HTML
        helper.querySelector('#copy-html').addEventListener('click', () => {
            copyToClipboard(document.documentElement.outerHTML, 'HTML已复制到剪贴板');
        });
        
        // 复制页面数据
        helper.querySelector('#copy-data').addEventListener('click', () => {
            const pageData = {
                url: window.location.href,
                title: document.title,
                html: document.documentElement.outerHTML,
                timestamp: new Date().toISOString(),
                pageType: detectPageType()
            };
            copyToClipboard(JSON.stringify(pageData, null, 2), '页面数据已复制到剪贴板');
        });
        
        // 复制清理后HTML
        helper.querySelector('#copy-clean').addEventListener('click', () => {
            const cleanHtml = cleanHtml(document.documentElement.outerHTML);
            copyToClipboard(cleanHtml, '清理后HTML已复制到剪贴板');
        });
        
        // 拖拽功能
        let isDragging = false;
        let startX, startY, startLeft, startTop;
        
        const header = helper.querySelector('div');
        header.addEventListener('mousedown', (e) => {
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            startLeft = helper.offsetLeft;
            startTop = helper.offsetTop;
        });
        
        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            
            const newLeft = startLeft + e.clientX - startX;
            const newTop = startTop + e.clientY - startY;
            
            helper.style.left = Math.max(0, Math.min(window.innerWidth - helper.offsetWidth, newLeft)) + 'px';
            helper.style.top = Math.max(0, Math.min(window.innerHeight - helper.offsetHeight, newTop)) + 'px';
            helper.style.right = 'auto';
        });
        
        document.addEventListener('mouseup', () => {
            isDragging = false;
        });
    }
    
    // 复制到剪贴板
    function copyToClipboard(text, message) {
        try {
            // 使用现代API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(() => {
                    showStatus(message, 'success');
                }).catch(() => {
                    fallbackCopy(text, message);
                });
            } else {
                fallbackCopy(text, message);
            }
        } catch (error) {
            fallbackCopy(text, message);
        }
    }
    
    // 备用复制方法
    function fallbackCopy(text, message) {
        try {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.opacity = '0';
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showStatus(message, 'success');
        } catch (error) {
            showStatus('复制失败，请手动复制', 'error');
            console.log('请手动复制以下内容:');
            console.log(text);
        }
    }
    
    // 显示状态消息
    function showStatus(message, type) {
        const statusDiv = document.querySelector('#copy-status');
        if (!statusDiv) return;
        
        statusDiv.textContent = message;
        statusDiv.style.display = 'block';
        statusDiv.style.backgroundColor = type === 'success' ? '#d4edda' : '#f8d7da';
        statusDiv.style.color = type === 'success' ? '#155724' : '#721c24';
        statusDiv.style.border = `1px solid ${type === 'success' ? '#c3e6cb' : '#f5c6cb'}`;
        
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }
    
    // 检测页面类型
    function detectPageType() {
        const url = window.location.href;
        if (url.includes('/search/results/people')) return 'people_search';
        if (url.includes('/search/results/companies')) return 'company_search';
        if (url.includes('/search/results/all')) return 'all_search';
        if (url.includes('/search/results/')) return 'search';
        if (url.includes('/in/')) return 'person_profile';
        if (url.includes('/company/')) return 'company_profile';
        if (url.includes('/overlay/contact-info/')) return 'contact_info';
        return 'unknown';
    }
    
    // 清理HTML（移除一些不必要的内容）
    function cleanHtml(html) {
        // 移除一些可能影响解析的内容
        return html
            .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '') // 移除script标签
            .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')   // 移除style标签
            .replace(/<!--[\s\S]*?-->/g, '')                   // 移除注释
            .replace(/\s+/g, ' ')                              // 压缩空白字符
            .trim();
    }
    
    // 创建复制助手
    createCopyHelper();
    
    // 提供全局函数
    window.LinkedInCopyHelper = {
        show: createCopyHelper,
        hide: () => {
            const helper = document.getElementById('linkedin-copy-helper');
            if (helper) helper.remove();
        },
        copyHtml: () => {
            copyToClipboard(document.documentElement.outerHTML, 'HTML已复制');
        },
        copyData: () => {
            const data = {
                url: window.location.href,
                html: document.documentElement.outerHTML,
                type: detectPageType()
            };
            copyToClipboard(JSON.stringify(data, null, 2), '数据已复制');
        }
    };
    
})();

console.log('🎉 LinkedIn复制助手已加载完成！');
console.log('💡 使用方法：');
console.log('   - 复制助手UI已自动显示在右上角');
console.log('   - 或者在控制台执行：LinkedInCopyHelper.copyHtml()');
console.log('   - 隐藏助手：LinkedInCopyHelper.hide()');
