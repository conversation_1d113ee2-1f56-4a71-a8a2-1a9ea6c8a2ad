<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn解析器调试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .full-width {
            grid-column: 1 / -1;
        }
        h1 {
            color: #0077b5;
            text-align: center;
            margin-bottom: 30px;
            grid-column: 1 / -1;
        }
        h2 {
            color: #333;
            margin-top: 0;
            border-bottom: 2px solid #0077b5;
            padding-bottom: 10px;
        }
        button {
            background: linear-gradient(135deg, #0077b5, #005582);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,119,181,0.3);
        }
        textarea, input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            transition: border-color 0.3s ease;
        }
        textarea:focus, input[type="text"]:focus {
            outline: none;
            border-color: #0077b5;
        }
        textarea {
            height: 150px;
            resize: vertical;
        }
        pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            overflow: auto;
            max-height: 400px;
            font-size: 12px;
            line-height: 1.4;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .log-entry {
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-info { background-color: #e3f2fd; color: #1976d2; }
        .log-error { background-color: #ffebee; color: #d32f2f; }
        .log-success { background-color: #e8f5e8; color: #388e3c; }
        .quick-test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 LinkedIn解析器调试工具</h1>
        
        <!-- 状态面板 -->
        <div class="panel">
            <h2>📊 解析器状态</h2>
            <div id="statusDisplay">
                <span class="status-indicator status-warning"></span>
                <span>检查中...</span>
            </div>
            <button onclick="checkParserStatus()">🔄 刷新状态</button>
            <button onclick="clearLogs()">🗑️ 清空日志</button>
            <pre id="statusDetails"></pre>
        </div>
        
        <!-- 快速测试面板 -->
        <div class="panel">
            <h2>⚡ 快速测试</h2>
            <div class="quick-test-buttons">
                <button onclick="testSearchParser()">🔍 测试搜索解析</button>
                <button onclick="testPersonParser()">👤 测试人物解析</button>
                <button onclick="testCompanyParser()">🏢 测试公司解析</button>
                <button onclick="testAllParsers()">🎯 测试全部</button>
            </div>
            <div class="stats" id="testStats">
                <div class="stat-card">
                    <span class="stat-number" id="testCount">0</span>
                    <span class="stat-label">测试次数</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number" id="successCount">0</span>
                    <span class="stat-label">成功次数</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number" id="errorCount">0</span>
                    <span class="stat-label">错误次数</span>
                </div>
            </div>
        </div>
        
        <!-- 自定义测试面板 -->
        <div class="panel full-width">
            <h2>🧪 自定义测试</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <label><strong>测试URL:</strong></label>
                    <input type="text" id="testUrl" placeholder="https://www.linkedin.com/..." value="https://www.linkedin.com/search/results/people/">
                    
                    <label style="margin-top: 15px; display: block;"><strong>测试HTML:</strong></label>
                    <textarea id="testHtml" placeholder="粘贴LinkedIn页面HTML..."></textarea>
                    
                    <div style="margin-top: 15px;">
                        <label><input type="checkbox" id="testIsZip"> 压缩数据</label>
                        <select id="testType" style="margin-left: 15px; padding: 8px;">
                            <option value="">自动识别</option>
                            <option value="search">搜索页</option>
                            <option value="person">人物页</option>
                            <option value="company">公司页</option>
                        </select>
                        <button onclick="runCustomTest()" style="margin-left: 15px;">🚀 运行测试</button>
                    </div>
                </div>
                
                <div>
                    <label><strong>解析结果:</strong></label>
                    <pre id="testResult" style="height: 300px;"></pre>
                </div>
            </div>
        </div>
        
        <!-- 日志面板 -->
        <div class="panel full-width">
            <h2>📝 调试日志</h2>
            <div id="debugLogs" style="max-height: 300px; overflow-y: auto; border: 1px solid #e9ecef; border-radius: 6px; padding: 10px;"></div>
        </div>
    </div>

    <!-- 加载解析器 -->
    <script src="parse_utils.js"></script>
    <script src="parse_models.js"></script>
    <script src="parse_search_page.js"></script>
    <script src="parse_human_info_page.js"></script>
    <script src="parse_company_info_page.js"></script>
    <script src="parse.js"></script>
    <script src="index.js"></script>
    
    <script>
        // 调试工具脚本
        let testCount = 0;
        let successCount = 0;
        let errorCount = 0;
        
        function log(message, type = 'info') {
            const logsContainer = document.getElementById('debugLogs');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }
        
        function updateStats() {
            document.getElementById('testCount').textContent = testCount;
            document.getElementById('successCount').textContent = successCount;
            document.getElementById('errorCount').textContent = errorCount;
        }
        
        function clearLogs() {
            document.getElementById('debugLogs').innerHTML = '';
            log('日志已清空', 'info');
        }
        
        function checkParserStatus() {
            try {
                const status = window.LinkedInParser.getStatus();
                const statusDisplay = document.getElementById('statusDisplay');
                const statusDetails = document.getElementById('statusDetails');
                
                if (status.initialized) {
                    statusDisplay.innerHTML = '<span class="status-indicator status-success"></span><span>解析器正常运行</span>';
                    log('解析器状态检查：正常', 'success');
                } else {
                    statusDisplay.innerHTML = '<span class="status-indicator status-error"></span><span>解析器初始化失败</span>';
                    log('解析器状态检查：失败', 'error');
                }
                
                statusDetails.textContent = JSON.stringify(status, null, 2);
            } catch (error) {
                document.getElementById('statusDisplay').innerHTML = '<span class="status-indicator status-error"></span><span>解析器未加载</span>';
                log(`状态检查错误: ${error.message}`, 'error');
            }
        }
        
        async function testSearchParser() {
            testCount++;
            updateStats();
            log('开始测试搜索解析器...', 'info');
            
            try {
                const mockHtml = '<div class="entity-result"><span class="entity-result__title"><a href="https://linkedin.com/in/test">Test User</a></span></div>';
                const result = await window.LinkedInParser.parse({
                    url: 'https://www.linkedin.com/search/results/people/',
                    html: mockHtml,
                    type: 'search'
                });
                
                successCount++;
                updateStats();
                log('搜索解析器测试成功', 'success');
                document.getElementById('testResult').textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                errorCount++;
                updateStats();
                log(`搜索解析器测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testPersonParser() {
            testCount++;
            updateStats();
            log('开始测试人物解析器...', 'info');
            
            try {
                const mockHtml = '<h1 class="text-heading-xlarge">Test Person</h1><script type="application/ld+json">{"@type":"Person","name":"Test"}</script>';
                const result = await window.LinkedInParser.parse({
                    url: 'https://www.linkedin.com/in/test-person',
                    html: mockHtml,
                    type: 'person'
                });
                
                successCount++;
                updateStats();
                log('人物解析器测试成功', 'success');
                document.getElementById('testResult').textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                errorCount++;
                updateStats();
                log(`人物解析器测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testCompanyParser() {
            testCount++;
            updateStats();
            log('开始测试公司解析器...', 'info');
            
            try {
                const mockHtml = '<script type="application/ld+json">{"@type":"Organization","name":"Test Company"}</script>';
                const result = await window.LinkedInParser.parse({
                    url: 'https://www.linkedin.com/company/test-company',
                    html: mockHtml,
                    type: 'company'
                });
                
                successCount++;
                updateStats();
                log('公司解析器测试成功', 'success');
                document.getElementById('testResult').textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                errorCount++;
                updateStats();
                log(`公司解析器测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testAllParsers() {
            log('开始全面测试...', 'info');
            await testSearchParser();
            await testPersonParser();
            await testCompanyParser();
            log('全面测试完成', 'success');
        }
        
        async function runCustomTest() {
            const url = document.getElementById('testUrl').value;
            const html = document.getElementById('testHtml').value;
            const isZip = document.getElementById('testIsZip').checked;
            const type = document.getElementById('testType').value;
            
            if (!url || !html) {
                log('请输入URL和HTML内容', 'error');
                return;
            }
            
            testCount++;
            updateStats();
            log(`开始自定义测试: ${url}`, 'info');
            
            try {
                const result = await window.LinkedInParser.parse({
                    url: url,
                    html: html,
                    isZip: isZip,
                    type: type || undefined
                });
                
                successCount++;
                updateStats();
                log('自定义测试成功', 'success');
                document.getElementById('testResult').textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                errorCount++;
                updateStats();
                log(`自定义测试失败: ${error.message}`, 'error');
                document.getElementById('testResult').textContent = `错误: ${error.message}`;
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('调试工具已加载', 'info');
            checkParserStatus();
            updateStats();
        });
        
        // 捕获全局错误
        window.addEventListener('error', function(event) {
            log(`全局错误: ${event.error.message}`, 'error');
        });
    </script>
</body>
</html>
