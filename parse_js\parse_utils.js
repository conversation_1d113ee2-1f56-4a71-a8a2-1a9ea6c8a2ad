/**
 * 解析工具函数 - JavaScript版本
 * 对应Python的public/parse_funcs.py
 */

/**
 * 自定义HTML实体解码函数
 * @param {string} text - 需要解码的文本
 * @param {Object} entities - 自定义实体映射表
 * @returns {string} 解码后的文本
 */
function customUnescape(text, entities = null) {
    if (!text) {
        return text;
    }
    
    // 先处理标准 HTML 实体
    const textarea = document.createElement('textarea');
    textarea.innerHTML = text;
    text = textarea.value;
    
    if (!entities) {
        // 创建实体替换字典
        entities = {
            '&l;': '<',
            '&lt;': '<',
            '&g;': '>',
            '&gt;': '>',
            '&q;': '"',
            '&ququot;': '"',
            '&quot;': '"',
            '&qquot;': '"',
            '&a;': '&'
        };
    }
    
    // 替换实体
    for (const [entity, char] of Object.entries(entities)) {
        try {
            text = text.replace(new RegExp(entity, 'g'), char);
        } catch (e) {
            // 忽略错误
        }
    }
    
    return text;
}

/**
 * 获取元素的文本内容
 * @param {Element} element - DOM元素
 * @returns {string} 文本内容
 */
function getElementText(element) {
    if (!element) return '';
    return element.textContent || element.innerText || '';
}

/**
 * 通过XPath查找元素
 * @param {string} xpath - XPath表达式
 * @param {Element} context - 上下文元素，默认为document
 * @returns {Array} 匹配的元素数组
 */
function getElementsByXPath(xpath, context = document) {
    const result = [];
    const xpathResult = document.evaluate(xpath, context, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
    
    for (let i = 0; i < xpathResult.snapshotLength; i++) {
        result.push(xpathResult.snapshotItem(i));
    }
    
    return result;
}

/**
 * 通过XPath获取单个元素的文本
 * @param {string} xpath - XPath表达式
 * @param {Element} context - 上下文元素
 * @returns {string} 元素文本
 */
function getTextByXPath(xpath, context = document) {
    const elements = getElementsByXPath(xpath, context);
    return elements.length > 0 ? getElementText(elements[0]) : '';
}

/**
 * 通过XPath获取多个元素的文本数组
 * @param {string} xpath - XPath表达式
 * @param {Element} context - 上下文元素
 * @returns {Array} 文本数组
 */
function getTextArrayByXPath(xpath, context = document) {
    const elements = getElementsByXPath(xpath, context);
    return elements.map(element => getElementText(element));
}

/**
 * 通过XPath获取属性值
 * @param {string} xpath - XPath表达式
 * @param {Element} context - 上下文元素
 * @returns {string} 属性值
 */
function getAttributeByXPath(xpath, context = document) {
    const elements = getElementsByXPath(xpath, context);
    return elements.length > 0 ? elements[0].nodeValue || elements[0].textContent : '';
}

/**
 * 安全的JSON解析
 * @param {string} jsonStr - JSON字符串
 * @returns {Object|null} 解析后的对象或null
 */
function safeJsonParse(jsonStr) {
    try {
        return JSON.parse(jsonStr);
    } catch (e) {
        console.warn('JSON解析失败:', e);
        return null;
    }
}

/**
 * URL解码
 * @param {string} url - 需要解码的URL
 * @returns {string} 解码后的URL
 */
function urlDecode(url) {
    try {
        return decodeURIComponent(url);
    } catch (e) {
        return url;
    }
}

/**
 * 提取LinkedIn用户名或公司ID
 * @param {string} url - LinkedIn URL
 * @returns {string} 用户名或公司ID
 */
function extractLinkedInId(url) {
    if (!url) return '';
    
    const match = url.match(/linkedin\.com\/(?:in|company)\/([^\/?]+)/);
    return match ? urlDecode(match[1]) : '';
}

/**
 * 清理和标准化文本
 * @param {string} text - 原始文本
 * @returns {string} 清理后的文本
 */
function cleanText(text) {
    if (!text) return '';
    
    return text
        .replace(/\n/g, '')
        .replace(/\t/g, '')
        .replace(/\s+/g, ' ')
        .trim();
}

// 导出所有函数
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        customUnescape,
        getElementText,
        getElementsByXPath,
        getTextByXPath,
        getTextArrayByXPath,
        getAttributeByXPath,
        safeJsonParse,
        urlDecode,
        extractLinkedInId,
        cleanText
    };
} else {
    // 浏览器环境
    window.ParseUtils = {
        customUnescape,
        getElementText,
        getElementsByXPath,
        getTextByXPath,
        getTextArrayByXPath,
        getAttributeByXPath,
        safeJsonParse,
        urlDecode,
        extractLinkedInId,
        cleanText
    };
}
