<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn解析器示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0077b5;
            margin-top: 0;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
        }
        button {
            background-color: #0077b5;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #005582;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin-bottom: 10px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
        pre {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            overflow: auto;
            max-height: 300px;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
        .status.success {
            background-color: #e6f7e6;
            color: #2e7d32;
        }
        .status.error {
            background-color: #fdecea;
            color: #c62828;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>LinkedIn解析器示例</h1>
        
        <div class="section">
            <h2>解析器状态</h2>
            <button id="checkStatus">检查状态</button>
            <span id="statusIndicator" class="status"></span>
            <pre id="statusOutput"></pre>
        </div>
        
        <div class="section">
            <h2>解析当前页面</h2>
            <button id="parseCurrent">解析当前页面</button>
            <pre id="parseOutput"></pre>
        </div>
        
        <div class="section">
            <h2>自定义解析</h2>
            <div>
                <label for="pageUrl">页面URL:</label>
                <input type="text" id="pageUrl" style="width: 100%; margin-bottom: 10px; padding: 8px;" placeholder="https://www.linkedin.com/...">
            </div>
            <div>
                <label for="pageHtml">页面HTML:</label>
                <textarea id="pageHtml" placeholder="粘贴HTML内容..."></textarea>
            </div>
            <div>
                <label>
                    <input type="checkbox" id="isZip"> 是压缩数据
                </label>
            </div>
            <div style="margin-top: 10px;">
                <label>解析类型:</label>
                <select id="parseType" style="padding: 6px;">
                    <option value="">自动识别</option>
                    <option value="search">搜索页</option>
                    <option value="person">人物页</option>
                    <option value="company">公司页</option>
                </select>
                <button id="parseCustom" style="margin-left: 10px;">解析</button>
            </div>
            <pre id="customOutput"></pre>
        </div>
    </div>

    <!-- 按顺序加载解析器文件 -->
    <script src="parse_utils.js"></script>
    <script src="parse_models.js"></script>
    <script src="parse_search_page.js"></script>
    <script src="parse_human_info_page.js"></script>
    <script src="parse_company_info_page.js"></script>
    <script src="parse.js"></script>
    <script src="index.js"></script>
    
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 检查状态按钮
            document.getElementById('checkStatus').addEventListener('click', function() {
                const status = window.LinkedInParser.getStatus();
                const statusOutput = document.getElementById('statusOutput');
                const statusIndicator = document.getElementById('statusIndicator');
                
                statusOutput.textContent = JSON.stringify(status, null, 2);
                
                if (status.initialized) {
                    statusIndicator.textContent = '正常';
                    statusIndicator.className = 'status success';
                } else {
                    statusIndicator.textContent = '错误';
                    statusIndicator.className = 'status error';
                }
            });
            
            // 解析当前页面按钮
            document.getElementById('parseCurrent').addEventListener('click', async function() {
                try {
                    const parseOutput = document.getElementById('parseOutput');
                    parseOutput.textContent = '解析中...';
                    
                    const html = document.documentElement.outerHTML;
                    const url = window.location.href;
                    
                    const result = await window.LinkedInParser.parse({
                        url: url,
                        html: html
                    });
                    
                    parseOutput.textContent = JSON.stringify(result, null, 2);
                } catch (error) {
                    document.getElementById('parseOutput').textContent = '解析错误: ' + error.message;
                }
            });
            
            // 自定义解析按钮
            document.getElementById('parseCustom').addEventListener('click', async function() {
                try {
                    const customOutput = document.getElementById('customOutput');
                    customOutput.textContent = '解析中...';
                    
                    const url = document.getElementById('pageUrl').value;
                    const html = document.getElementById('pageHtml').value;
                    const isZip = document.getElementById('isZip').checked;
                    const type = document.getElementById('parseType').value;
                    
                    if (!url || !html) {
                        customOutput.textContent = '错误: URL和HTML内容不能为空';
                        return;
                    }
                    
                    const result = await window.LinkedInParser.parse({
                        url: url,
                        html: html,
                        isZip: isZip,
                        type: type || undefined
                    });
                    
                    customOutput.textContent = JSON.stringify(result, null, 2);
                } catch (error) {
                    document.getElementById('customOutput').textContent = '解析错误: ' + error.message;
                }
            });
            
            // 自动检查状态
            document.getElementById('checkStatus').click();
        });
    </script>
</body>
</html>
