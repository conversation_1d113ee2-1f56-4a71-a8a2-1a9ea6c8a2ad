import logging
import re
import json
import traceback
import urllib.parse
from lxml import etree
from link.parse_models import *
from public.parse_funcs import custom_unescape
from typing import Tuple


class SearchAllParse:
    """
        link综合搜索页解析
    """

    def __init__(self):
        # 一个解析 领英人物公司链接ID的正则
        self.match_person_company_username = re.compile(r'linkedin\.com\/(?:in|company)\/([^\/?]+)')

    def parse_search_people(self, data: dict) -> Union[ParseSearchAllPeople, None]:
        """
        解析 搜索页 人物json
        """
        # 解析 链接 和 ID
        tracking_urn = data.get('trackingUrn', '')
        linkedin_id = tracking_urn.split(':')[-1]
        if linkedin_id in ['headless']:
            # 需要会员；名称等信息未展示
            return None
        navigation_url = data.get('navigationUrl', '')
        # navigation_url = urllib.parse.unquote(navigation_url) # 解码会出现 emoji 图片
        linkedin_url = navigation_url.split('?')[0].strip('/').replace('https://www.', '') if navigation_url else ''
        linkedin_username_match = self.match_person_company_username.findall(linkedin_url) if linkedin_url else []
        linkedin_username = urllib.parse.unquote(linkedin_username_match[0]) if linkedin_username_match else ''
        # 解析 人物名称
        title_data = data.get('title', {})
        user_name = title_data.get('text', '')
        # 解析 logo
        image_data = data.get('image', {})
        attributes = image_data.get('attributes', [])
        detail_data = attributes[0].get('detailData', {}) if attributes else {}
        non_entity_picture = detail_data.get('nonEntityProfilePicture', {})
        vector_image = non_entity_picture.get('vectorImage', {}) if non_entity_picture else {}
        if vector_image:
            root_url = vector_image.get('rootUrl', '')
            artifacts = vector_image.get('artifacts', [])
            logos = list(set([root_url + artifact.get('fileIdentifyingUrlPathSegment', '') for artifact in artifacts if
                              artifact.get('fileIdentifyingUrlPathSegment', '')]))
        else:
            logos = []
        # 解析 简介
        summary_data = data.get('summary', {})
        if summary_data:
            profiles = summary_data.get('text', '') or ''
        else:
            primary_subtitle_data = data.get('primarySubtitle', {})
            profiles = primary_subtitle_data.get('text', '') if primary_subtitle_data else ''
        profiles = profiles.strip('-')
        # 解析 地区
        secondary_subtitle_data = data.get('secondarySubtitle', {}) or {}
        region = secondary_subtitle_data.get('text', '')
        parse_data = {
            'linkedin_id': linkedin_id,
            'linkedin_username': linkedin_username,
            'linkedin_url': linkedin_url,
            'full_name': user_name,
            'person_logos': json.dumps(logos, ensure_ascii=False) if logos else '',
            'person_profiles': profiles,
            'region': region
        }
        return ParseSearchAllPeople(**parse_data)

    def parse_search_company(self, data: dict) -> Union[ParseSearchAllCompany, None]:
        """
        解析 搜索页 公司json
        """
        # 解析 链接 和 ID
        tracking_urn = data.get('trackingUrn', '')
        job_company_linkedin_id = tracking_urn.split(':')[-1]
        navigation_url = data.get('navigationUrl', '')
        # navigation_url = urllib.parse.unquote(navigation_url) # 解码会出现 emoji 图片
        linkedin_url = navigation_url.split('?')[0].strip('/').replace('https://www.', '') if navigation_url else ''
        linkedin_username_match = self.match_person_company_username.findall(linkedin_url) if linkedin_url else []
        linkedin_username = urllib.parse.unquote(linkedin_username_match[0]) if linkedin_username_match else ''
        # 解析 公司名称
        title_data = data.get('title', {}) or {}
        user_name = title_data.get('text', '') or ''
        # 解析 logo
        image_data = data.get('image', {}) or {}
        attributes = image_data.get('attributes', []) or []
        detail_data = attributes[0].get('detailData', {}) if attributes else {}
        non_entity_picture = detail_data.get('nonEntityCompanyLogo', {}) or {}
        vector_image = non_entity_picture.get('vectorImage', {}) if non_entity_picture else {}
        if vector_image:
            root_url = vector_image.get('rootUrl', '')
            artifacts = vector_image.get('artifacts', [])
            logos = list(set([root_url + artifact.get('fileIdentifyingUrlPathSegment', '') for artifact in artifacts if
                              artifact.get('fileIdentifyingUrlPathSegment', '')]))
        else:
            logos = []
        # 解析 简介
        summary_data = data.get('summary', {}) or {}
        profiles = summary_data.get('text', '')
        # 解析 行业与地区
        primary_subtitle_data = data.get('primarySubtitle', {})
        industry_region = primary_subtitle_data.get('text', '') if primary_subtitle_data else ''
        industry_region_list = industry_region.split('•') or []
        if len(industry_region_list) >= 2:
            industry = industry_region_list[0].strip()
            region = industry_region_list[1].strip()
        else:
            industry = ''
            region = ''
        # 解析 岗位信息
        insights_resolution_results_data = data.get('insightsResolutionResults', []) or []
        insights_resolution_result = insights_resolution_results_data[0] if insights_resolution_results_data else {}
        simple_insight_data = insights_resolution_result.get('simpleInsight', {}) or {}
        # 职位搜索链接
        job_search_url = simple_insight_data.get('navigationUrl', '') or ''
        # 职位数量
        job_title_data = simple_insight_data.get('title', {}) or {}
        job_num = job_title_data.get('text', '') or ''
        job_num = job_num.replace('jobs', '').replace('job', '').strip()
        # 判断是否纯数字
        if not job_num.isdigit():
            job_num = ''
        # 解析 关注数量
        secondary_subtitle_data = data.get('secondarySubtitle', {}) or {}
        followers = secondary_subtitle_data.get('text', '') if secondary_subtitle_data else {}
        followers = followers.replace('followers', '').replace('follower', '').strip()

        parse_data = {
            'job_company_id': linkedin_username,
            'job_company_linkedin_id': job_company_linkedin_id,
            'job_company_linkedin_url': linkedin_url,
            'job_company_name': user_name,
            'company_logos': json.dumps(logos, ensure_ascii=False) if logos else '',
            'company_profiles': profiles,
            'job_search_url': job_search_url,
            'job_num': job_num,
            'followers': followers,
            'industry': industry,
            'region': region,
        }
        # print(parse_data)
        return ParseSearchAllCompany(**parse_data)

    def parse_search_company_dom(self, html_xpath: str) -> Union[List[ParseSearchAllCompany], None]:
        """
            解析 公司搜索页 DOM
        """
        datalist = []
        li_dom_list = html_xpath.xpath('.//ul[@role="list"]/li')
        for li_dom in li_dom_list:
            mb1_doms = li_dom.xpath('.//div[@class="t-roman t-sans"]')
            if not mb1_doms:
                continue
            # 解析 公司名称与链接
            mb1_dom = mb1_doms[0]
            # navigation_url = mb1_dom.xpath('.//a[starts-with(@class, "app-aware-link")]/@href')[0]
            navigation_url = mb1_dom.xpath('.//a/@href')[0]
            job_company_name = mb1_dom.xpath('string(.//a)').strip()
            linkedin_url = navigation_url.split('?')[0].strip('/').replace('https://www.', '') if navigation_url else ''
            linkedin_username_match = self.match_person_company_username.findall(
                linkedin_url) if linkedin_url else []
            linkedin_username = urllib.parse.unquote(
                linkedin_username_match[0]) if linkedin_username_match else ''
            if not linkedin_username:
                continue
            # 解析 公司logo
            logos = li_dom.xpath('.//div[@class="display-flex align-items-center"]//img/@src')
            # 解析行业与地区
            industry_region = mb1_dom.xpath('string(.//div[contains(@class, "t-14 t-black t-normal")])').strip()
            industry_region_list = industry_region.split(' • ')
            if len(industry_region_list) >= 2:
                industry = industry_region_list[0].strip()
                region = industry_region_list[1].strip()
            else:
                industry = ''
                region = ''
            # 解析 粉丝数
            followers = mb1_dom.xpath('string(.//div[contains(@class, "t-14 t-normal")])').strip()
            followers = followers.replace('followers', '').replace('follower', '').replace('位关注者', '').strip()
            # 解析 公司简介
            profiles = li_dom.xpath('string(.//p[contains(@class, "t-12 t-black--light")])').strip()
            # 解析职位链接
            job_search_url_list = li_dom.xpath('.//div[starts-with(@class, "entity-result__insights")]//a/@href')
            job_search_url = job_search_url_list[0] if job_search_url_list else ''
            # 解析职位数量
            job_num = li_dom.xpath('string(.//div[starts-with(@class, "entity-result__insights")]//a)').strip()
            job_num = job_num.replace('jobs', '').replace('job', '').replace('个职位', '').strip()
            # 意外情况
            if len(followers) > 10:
                followers = ''
            if len(job_num) > 10:
                job_num = ''

            parse_data = {
                'job_company_id': linkedin_username,
                'job_company_linkedin_url': linkedin_url,
                'job_company_name': job_company_name,
                'company_logos': json.dumps(logos, ensure_ascii=False) if logos else '',
                'company_profiles': profiles,
                'job_search_url': job_search_url,
                'job_num': job_num,
                'followers': followers,
                'industry': industry,
                'region': region,
            }
            print(parse_data)
            datalist.append(ParseSearchAllCompany(**parse_data))
        return datalist

    def parse_search_person_dom(self, html_xpath: str) -> Union[List[ParseSearchAllPeople], None]:
        """
            解析 人物搜索页 DOM
        """
        datalist = []
        li_dom_list = html_xpath.xpath('.//ul[@role="list"]/li')
        for li_dom in li_dom_list:
            mb1_doms = li_dom.xpath('.//div[@class="t-roman t-sans"]')
            if not mb1_doms:
                continue
            # 解析 名称与链接
            mb1_dom = mb1_doms[0]
            # navigation_url = mb1_dom.xpath('.//a[starts-with(@class, "app-aware-link")]/@href')[0]
            navigation_url = mb1_dom.xpath('.//a/@href')[0]
            user_name = mb1_dom.xpath('string(.//a/span/span)').strip()
            linkedin_url = navigation_url.split('?')[0].strip('/').replace('https://www.', '') if navigation_url else ''
            linkedin_username_match = self.match_person_company_username.findall(
                linkedin_url) if linkedin_url else []
            linkedin_username = urllib.parse.unquote(
                linkedin_username_match[0]) if linkedin_username_match else ''
            if not linkedin_username:
                continue
            # 解析 人物logo
            logos = li_dom.xpath('.//div[@class="display-flex align-items-center"]//img/@src')
            # 解析 地区
            region = li_dom.xpath(
                'string(.//div[contains(@class, "t-14 t-normal")])').strip()
            # 解析 简介
            profiles = li_dom.xpath('string(.//div[contains(@class, "t-14 t-black t-normal")])').strip()
            parse_data = {
                'linkedin_id': '',
                'linkedin_username': linkedin_username,
                'linkedin_url': linkedin_url,
                'full_name': user_name,
                'person_logos': json.dumps(logos, ensure_ascii=False) if logos else '',
                'person_profiles': profiles,
                'region': region
            }
            datalist.append(ParseSearchAllPeople(**parse_data))
        return datalist

    def parse_search_posts(self, data: dict) -> Union[ParseSearchAllPost, None]:
        """
        解析 搜索页 帖子json
        """
        # 解析 链接 和 ID
        metadata = data.get('metadata', {})
        social_content_data = data.get('socialContent', {})
        backend_urn = metadata.get('backendUrn', '') if metadata else ''
        post_id = backend_urn.split(':')[-1]
        share_url = social_content_data.get('shareUrl', '')
        # share_url = urllib.parse.unquote(share_url)   # 解码会出现 emoji 图片
        # 解析 帖子 图片
        content_data = data.get('content', {})
        article_component_data = content_data.get('articleComponent', {}) if content_data else {}  # 文章组件
        image_component_data = content_data.get('imageComponent', {}) if content_data else {}  # 图片组件
        link_video_component_data = content_data.get('linkedInVideoComponent', {}) if content_data else {}  # 领英视频组件
        image_data1 = article_component_data.get('smallImage', {}) if article_component_data else {}
        image_data_list = image_component_data.get('images', []) if image_component_data else []
        images = []
        if image_data1:
            image_data_list.append(image_data1)
        for _image_data in image_data_list:
            attributes = image_data1.get('attributes', [])
            if not attributes:
                continue
            for attribute in attributes:
                detail_data = attribute.get('detailData', {}) if attributes else {}
                vector_image = detail_data.get('vectorImage', {})
                if vector_image:
                    root_url = vector_image.get('rootUrl', '')
                    artifacts = vector_image.get('artifacts', [])
                    images += [root_url + artifact.get('fileIdentifyingUrlPathSegment', '') for artifact in artifacts if
                               artifact.get('fileIdentifyingUrlPathSegment', '')]
        images = list(set(images))
        # 解析 标题
        title_data = article_component_data.get('title', {}) if article_component_data else {}
        subtitle_data = article_component_data.get('subtitle', {}) if article_component_data else {}
        title = title_data.get('text', '') if title_data else ''
        subtitle = subtitle_data.get('text', '') if subtitle_data else ''
        # 解析帖子内容
        navigation_context_data = article_component_data.get('navigationContext', {}) if article_component_data else {}
        action_target = navigation_context_data.get('actionTarget', '') if navigation_context_data else ''
        accessibility_text = navigation_context_data.get('accessibilityText', '') if navigation_context_data else ''
        if not accessibility_text:
            commentary_data = data.get('commentary', {})
            commentary_text_data = commentary_data.get('text', {}) if commentary_data else {}
            accessibility_text = commentary_text_data.get('text', '')

        # 解析发帖人名称、ID与头像
        actor_data = data.get('actor', {})
        # 获取 名称
        user_name_data = actor_data.get('name', {})
        if user_name_data:
            user_name = user_name_data.get('text', '')
            if not user_name:
                user_name = user_name_data.get('accessibilityText', '')
        else:
            user_name = ''
        # 判断是 人还是公司
        actor_backend_urn = actor_data.get('backendUrn', '') if actor_data else ''
        image_data = actor_data.get('image', {}) if actor_data else {}
        attributes = image_data.get('attributes', [])
        detail_data = attributes[0].get('detailData', {}) if attributes else {}
        linkedin_id, job_company_linkedin_id = '', ''  # ID
        non_entity_picture = {}  # 头像
        is_person = True
        if actor_backend_urn and 'urn:li:member' in actor_backend_urn:
            linkedin_id = actor_backend_urn.split(':')[-1]
            non_entity_picture = detail_data.get('nonEntityProfilePicture', {})
        elif actor_backend_urn and 'urn:li:company' in actor_backend_urn:
            job_company_linkedin_id = actor_backend_urn.split(':')[-1]
            non_entity_picture = detail_data.get('nonEntityCompanyLogo', {})
            is_person = False

        # 解析发帖人或公司头像
        actor_vector_image = non_entity_picture.get('vectorImage', {}) if non_entity_picture else {}
        if actor_vector_image:
            root_url = actor_vector_image.get('rootUrl', '')
            artifacts = actor_vector_image.get('artifacts', [])
            logos = list(set([root_url + artifact.get('fileIdentifyingUrlPathSegment', '') for artifact in artifacts if
                              artifact.get('fileIdentifyingUrlPathSegment', '')]))
        else:
            logos = []

        # 解析 发帖人 链接
        actor_navigation_context_data = actor_data.get('navigationContext', {})
        navigation_url = actor_navigation_context_data.get('actionTarget', '')
        person_company_url = navigation_url.split('?')[0].replace('https://www.', '').strip('posts').strip(
            '/') if navigation_url else ''

        # 解析 人物简介
        description_data = actor_data.get('description', {})
        if description_data:
            profiles = description_data.get('text', '')
            if not profiles:
                profiles = description_data.get('accessibilityText', '')
        else:
            profiles = ''

        link_user_name_match = self.match_person_company_username.findall(person_company_url)
        link_user_name = link_user_name_match[0] if link_user_name_match else ''
        # 判断是人还是公司发帖进行赋值
        if is_person:
            people_data = {
                'linkedin_id': linkedin_id,
                'linkedin_username': link_user_name,
                'linkedin_url': person_company_url,
                'full_name': user_name,
                'person_logos': json.dumps(logos, ensure_ascii=False) if logos else '',
                'person_profiles': profiles,
            }
            people_model = ParseSearchAllPeople(**people_data)
            company_model = None
        else:
            company_data = {
                'job_company_linkedin_id': job_company_linkedin_id,
                'job_company_id': link_user_name,
                'job_company_linkedin_url': person_company_url,
                'job_company_name': user_name,
                'company_logos': json.dumps(logos, ensure_ascii=False) if logos else '',
                'company_profiles': profiles,
            }
            people_model = None
            company_model = ParseSearchAllCompany(**company_data)

        parse_data = {
            'post_id': post_id,
            'people_model': people_model,
            'company_model': company_model,
            'post_title': title,
            'post_subtitle': subtitle,
            'post_share_url': share_url,
            'post_images': json.dumps(images, ensure_ascii=False) if images else '',
            'post_action_target': action_target,
            'post_accessibility_text': accessibility_text,
        }
        return ParseSearchAllPost(**parse_data)

    def parse_search_people_followee(self, data: dict) -> Union[ParseSearchAllPeopleFollowee, None]:
        """
        解析 搜索页 人物关注情况视图 JSON
        """
        tracking_urn = data.get('trackingUrn', '')
        linkedin_id = tracking_urn.split(':')[-1]
        follower_count = data.get('followerCount', 0)
        if not follower_count:
            return None
        parse_data = {
            'linkedin_id': linkedin_id,
            'follower_count': follower_count,
        }
        return ParseSearchAllPeopleFollowee(**parse_data)

    def parse_search_company_followee(self, data: dict) -> Union[ParseSearchAllCompanyFollowee, None]:
        """
        解析 搜索页 公司关注情况视图 JSON
        """
        tracking_urn = data.get('trackingUrn', '')
        job_company_linkedin_id = tracking_urn.split(':')[-1]
        follower_count = data.get('followerCount', 0)
        if not follower_count:
            return None
        parse_data = {
            'job_company_linkedin_id': job_company_linkedin_id,
            'follower_count': follower_count,
        }
        return ParseSearchAllCompanyFollowee(**parse_data)

    def parse_search_post_count(self, data: dict) -> Union[ParseSearchAllPostCount, None]:
        """
        解析 搜索页 帖子 统计视图 JSON
        """
        urn = data.get('urn', '')
        post_id = urn.split(':')[-1]
        like_num = data.get('numLikes', 0)
        comments_num = data.get('numComments', 0)
        shares_num = data.get('numShares', 0)
        if not post_id:
            return None
        parse_data = {
            'post_id': post_id,
            'like_num': like_num,
            'comments_num': comments_num,
            'shares_num': shares_num,
        }
        return ParseSearchAllPostCount(**parse_data)

    def search_all_page_parser_json(self, json_str: str) -> Tuple[dict, bool]:
        """
            全局搜索 列表页 解析 - 接口返回的结果
        """
        data = {
            'company_models': {},
            'people_models': {},
            'post_models': {},
            'company_followee_models': {},
            'people_followee_models': {},
            'post_count_models': {},
        }
        try:
            data_json = json.loads(json_str)
            metadata = data_json.get('data', {}).get('data', {}).get('searchDashClustersByAll', {}).get('metadata',
                                                                                                        {})
            included = data_json.get('included', [])
            if not metadata or not included:
                return data, False
            for _d in included:
                # print(json.dumps(_d, ensure_ascii=False))
                urn = _d.get('urn', '')
                entity_urn = _d.get('entityUrn', '')
                tracking_urn = _d.get('trackingUrn', '')
                metadata = _d.get('metadata', {})
                backend_urn = metadata.get('backendUrn', '') if metadata else ''
                if 'urn:li:company' in tracking_urn and 'urn:li:fsd_entityResultViewModel' in entity_urn:
                    # 这是公司 结果视图
                    parse_data = self.parse_search_company(_d)
                    if parse_data:
                        data['company_models'][parse_data.job_company_id] = parse_data
                elif 'urn:li:company' in tracking_urn and 'urn:li:fsd_followingState' in entity_urn:
                    # 这是公司 关注情况视图
                    parse_data = self.parse_search_company_followee(_d)
                    if parse_data:
                        data['company_followee_models'][parse_data.job_company_linkedin_id] = parse_data
                elif 'urn:li:member' in tracking_urn and 'urn:li:fsd_entityResultViewModel' in entity_urn:
                    # 这是人物 结果视图
                    parse_data = self.parse_search_people(_d)
                    if parse_data:
                        data['people_models'][parse_data.linkedin_id] = parse_data
                elif 'urn:li:member' in tracking_urn and 'urn:li:fsd_followingState' in entity_urn:
                    # 这是人物 关注情况视图
                    parse_data = self.parse_search_people_followee(_d)
                    if parse_data:
                        data['people_followee_models'][parse_data.linkedin_id] = parse_data
                elif 'urn:li:activity' in backend_urn and 'urn:li:fsd_update' in entity_urn:
                    # 这是 posts 帖子 结果视图
                    parse_data = self.parse_search_posts(_d)
                    if parse_data:
                        data['post_models'][parse_data.post_id] = parse_data
                        if parse_data.people_model:
                            data['people_models'][parse_data.people_model.linkedin_id] = parse_data.people_model
                        if parse_data.company_model:
                            data['company_models'][
                                parse_data.company_model.job_company_id] = parse_data.company_model
                elif 'urn:li:activity' in urn and 'urn:li:fsd_socialActivityCounts' in entity_urn:
                    # 这是 posts 帖子 统计视图
                    parse_data = self.parse_search_post_count(_d)
                    if parse_data:
                        data['post_count_models'][parse_data.post_id] = parse_data
                else:
                    continue
        except Exception as e:
            logging.warning(traceback.format_exc())
            pass
        return data, True

    def search_all_page_parser_html_company(self, html_xpath):
        """
            全局搜索 公司列表页 解析 - 源代码
        """
        data = {
            'company_models': {},
            'people_models': {},
            'post_models': {},
            'company_followee_models': {},
            'people_followee_models': {},
            'post_count_models': {},
        }
        try:
            parse_datalist = self.parse_search_company_dom(html_xpath)
            for parse_data in parse_datalist:
                data['company_models'][parse_data.job_company_id] = parse_data
        except Exception as e:
            print(traceback.format_exc())
            pass
        return data, True

    def search_all_page_parser_html_person(self, html_xpath):
        """
            全局搜索 人物列表页 解析 - 源代码
        """
        data = {
            'company_models': {},
            'people_models': {},
            'post_models': {},
            'company_followee_models': {},
            'people_followee_models': {},
            'post_count_models': {},
        }
        try:
            parse_datalist = self.parse_search_person_dom(html_xpath)
            for parse_data in parse_datalist:
                data['people_models'][parse_data.linkedin_username] = parse_data
        except Exception as e:
            pass
        return data, True

    def search_all_page_parser_html_all(self, html_xpath: str):
        """
            解析 全部搜索页 DOM
        """
        res_data = {
            'company_models': {},
            'people_models': {},
            'post_models': {},
            'company_followee_models': {},
            'people_followee_models': {},
            'post_count_models': {},
        }
        result_containers = html_xpath.xpath('//div[@class="search-results-container"]/div')
        for result_container in result_containers:
            h2 = result_container.xpath('string(.//h2[contains(@class, "search-results__cluster-title")])').strip().lower()
            if h2 in ('companies', '公司'):
                parse_datalist = self.parse_search_company_dom(result_container)
                for parse_data in parse_datalist:
                    res_data['company_models'][parse_data.job_company_id] = parse_data
            elif h2 in ('people', '会员', '更多会员', 'more people'):
                parse_datalist = self.parse_search_person_dom(result_container)
                for parse_data in parse_datalist:
                    res_data['people_models'][parse_data.linkedin_username] = parse_data
            elif h2 == 'posts':
                pass
            else:
                continue
        return res_data, True

    def search_all_page_parser_html(self, html_str: str, from_type='html') -> dict:
        """
            全局搜索 列表页 解析 - 源代码
        """
        res_data = {
            'company_models': {},
            'people_models': {},
            'post_models': {},
            'company_followee_models': {},
            'people_followee_models': {},
            'post_count_models': {},
        }
        html_str = custom_unescape(html_str)
        html_xpath = etree.HTML(html_str)
        if from_type == 'html':
            # 通过 html 中的 json 解析
            code_list = html_xpath.xpath("//code")
            for code_ele in code_list:
                code_str = code_ele.xpath("string(.)")
                parse_data, status = self.search_all_page_parser_json(code_str)
                if status:
                    return parse_data
        # 通过 HTML DOM 结构解析
        if from_type == 'html_all':
            # 解析全部搜索页HTML DOM
            parse_data, status = self.search_all_page_parser_html_all(html_xpath)
            if status:
                return parse_data
        if from_type == 'html_company':
            # 解析公司搜索页HTML DOM
            parse_data, status = self.search_all_page_parser_html_company(html_xpath)
            if status:
                return parse_data
        if from_type == 'html_people':
            # 解析人物搜索页HTML DOM
            parse_data, status = self.search_all_page_parser_html_person(html_xpath)
            if status:
                return parse_data
        return res_data


if __name__ == '__main__':
    search_all_parse_obj = SearchAllParse()
    with open('../test/link_search_all_v3.html', 'r', encoding='utf-8') as f:
        html_str = f.read()
    print(f"Type of html_str: {type(html_str)}")
    print(f"Value of html_str: {repr(html_str)}")

    # html_str = custom_unescape(html_str)
    html_xpath = etree.HTML(html_str)
    data = search_all_parse_obj.search_all_page_parser_html(html_xpath, from_type='html_all')

    # with open('../test/search_all_res.json', 'r', encoding='utf-8') as f:
    #     json_str = f.read()
    # data, status = search_all_parse_obj.search_all_page_parser_json(json_str)
    print(data)
