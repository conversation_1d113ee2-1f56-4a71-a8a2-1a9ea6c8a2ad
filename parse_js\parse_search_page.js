/**
 * LinkedIn搜索页面解析 - JavaScript版本
 * 对应Python的link/parse_search_page.py
 */

// 导入依赖（在浏览器环境中通过script标签引入）
// const { ParseSearchAllPeople, ParseSearchAllCompany } = require('./parse_models.js');
// const { customUnescape, getElementsByXPath, getTextByXPath, safeJsonParse, extractLinkedInId, cleanText } = require('./parse_utils.js');

class SearchAllParse {
    constructor() {
        // 一个解析 领英人物公司链接ID的正则
        this.matchPersonCompanyUsername = /linkedin\.com\/(?:in|company)\/([^\/?]+)/;
    }

    /**
     * 解析搜索页人物JSON数据
     * @param {Object} data - 人物数据对象
     * @returns {ParseSearchAllPeople|null} 解析后的人物数据
     */
    parseSearchPeople(data) {
        // 解析链接和ID
        const trackingUrn = data.trackingUrn || '';
        const linkedinId = trackingUrn.split(':').pop();
        
        if (linkedinId === 'headless') {
            // 需要会员；名称等信息未展示
            return null;
        }
        
        const navigationUrl = data.navigationUrl || '';
        const linkedinUrl = navigationUrl ? 
            navigationUrl.split('?')[0].replace(/\/$/, '').replace('https://www.', '') : '';
        
        const linkedinUsernameMatch = this.matchPersonCompanyUsername.exec(linkedinUrl);
        const linkedinUsername = linkedinUsernameMatch ? 
            decodeURIComponent(linkedinUsernameMatch[1]) : '';
        
        // 解析人物名称
        const titleData = data.title || {};
        const humanName = titleData.text || '';
        
        // 解析人物简介
        const primarySubtitle = data.primarySubtitle || {};
        const humanProfiles = primarySubtitle.text || '';
        
        // 解析人物头像
        const image = data.image || {};
        const humanAvatars = JSON.stringify([{
            url: image.url || '',
            width: image.attributes?.[0]?.detailData?.nonEntityProfilePicture?.vectorImage?.artifacts?.[0]?.width || '',
            height: image.attributes?.[0]?.detailData?.nonEntityProfilePicture?.vectorImage?.artifacts?.[0]?.height || ''
        }]);
        
        // 解析工作信息
        const secondarySubtitle = data.secondarySubtitle || {};
        const jobInfo = secondarySubtitle.text || '';
        
        // 解析公司信息
        let jobCompanyName = '';
        let jobTitle = '';
        let jobLocation = '';
        
        if (jobInfo) {
            const jobParts = jobInfo.split(' at ');
            if (jobParts.length >= 2) {
                jobTitle = jobParts[0].trim();
                jobCompanyName = jobParts[1].trim();
            }
        }
        
        // 解析位置信息
        const snippet = data.snippet || {};
        const snippetText = snippet.text || '';
        if (snippetText && !jobLocation) {
            jobLocation = snippetText;
        }
        
        // 解析共同联系人
        const socialProofText = data.socialProofText || '';
        const mutualConnectionsMatch = socialProofText.match(/(\d+)/);
        const mutualConnectionsCount = mutualConnectionsMatch ? mutualConnectionsMatch[1] : '';
        
        // 检查是否为高级会员
        const isPremium = data.badges?.some(badge => 
            badge.type === 'PREMIUM' || badge.text?.toLowerCase().includes('premium')
        ) || false;
        
        return new (window.ParseModels?.ParseSearchAllPeople || ParseSearchAllPeople)({
            linkedin_id: linkedinId,
            linkedin_username: linkedinUsername,
            linkedin_url: linkedinUrl,
            human_name: humanName,
            human_avatars: humanAvatars,
            human_profiles: humanProfiles,
            job_title: jobTitle,
            job_company_name: jobCompanyName,
            job_location: jobLocation,
            mutual_connections_count: mutualConnectionsCount,
            is_premium: isPremium
        });
    }

    /**
     * 解析搜索页公司JSON数据
     * @param {Object} data - 公司数据对象
     * @returns {ParseSearchAllCompany|null} 解析后的公司数据
     */
    parseSearchCompany(data) {
        // 解析链接和ID
        const trackingUrn = data.trackingUrn || '';
        const linkedinId = trackingUrn.split(':').pop();
        
        const navigationUrl = data.navigationUrl || '';
        const linkedinUrl = navigationUrl ? 
            navigationUrl.split('?')[0].replace(/\/$/, '').replace('https://www.', '') : '';
        
        const linkedinUsernameMatch = this.matchPersonCompanyUsername.exec(linkedinUrl);
        const jobCompanyId = linkedinUsernameMatch ? 
            decodeURIComponent(linkedinUsernameMatch[1]) : '';
        
        // 解析公司名称
        const titleData = data.title || {};
        const jobCompanyName = titleData.text || '';
        
        // 解析公司简介
        const primarySubtitle = data.primarySubtitle || {};
        const companyProfiles = primarySubtitle.text || '';
        
        // 解析公司logo
        const image = data.image || {};
        const companyLogos = JSON.stringify([{
            url: image.url || '',
            width: image.attributes?.[0]?.detailData?.company?.logo?.image?.com?.linkedin?.common?.VectorImage?.artifacts?.[0]?.width || '',
            height: image.attributes?.[0]?.detailData?.company?.logo?.image?.com?.linkedin?.common?.VectorImage?.artifacts?.[0]?.height || ''
        }]);
        
        // 解析公司信息
        const secondarySubtitle = data.secondarySubtitle || {};
        const companyInfo = secondarySubtitle.text || '';
        
        let companySize = '';
        let companyIndustry = '';
        let companyLocation = '';
        
        if (companyInfo) {
            const infoParts = companyInfo.split(' • ');
            infoParts.forEach(part => {
                if (part.includes('employees') || part.includes('人')) {
                    companySize = part.trim();
                } else if (!companyLocation) {
                    companyLocation = part.trim();
                }
            });
        }
        
        // 解析关注者数量
        const socialProofText = data.socialProofText || '';
        const followersMatch = socialProofText.match(/(\d+)/);
        const followers = followersMatch ? followersMatch[1] : '';
        
        // 解析职位搜索链接
        const jobSearchUrl = data.jobSearchUrl || '';
        
        return new (window.ParseModels?.ParseSearchAllCompany || ParseSearchAllCompany)({
            job_company_id: jobCompanyId,
            job_company_linkedin_id: linkedinId,
            job_company_linkedin_url: linkedinUrl,
            job_company_name: jobCompanyName,
            company_logos: companyLogos,
            company_profiles: companyProfiles,
            job_search_url: jobSearchUrl,
            followers: followers,
            company_size: companySize,
            company_industry: companyIndustry,
            company_location: companyLocation
        });
    }

    /**
     * 从JSON字符串解析搜索页数据
     * @param {string} jsonStr - JSON字符串
     * @returns {Object} 解析结果 {people: [], companies: [], status: boolean}
     */
    searchAllPageParserJson(jsonStr) {
        const utils = window.ParseUtils || { safeJsonParse, customUnescape };

        try {
            const data = utils.safeJsonParse(jsonStr);
            if (!data || !data.data) {
                return { people: [], companies: [], status: false };
            }

            const searchResults = data.data.searchDashClustersByAll ||
                                data.data.searchDashClustersByPeople ||
                                data.data.searchDashClustersByCompanies || {};

            const elements = searchResults.elements || [];
            const people = [];
            const companies = [];

            elements.forEach(element => {
                const items = element.items || [];
                items.forEach(item => {
                    const entityResult = item.item?.entityResult;
                    if (!entityResult) return;

                    if (entityResult.__typename === 'Person') {
                        const personData = this.parseSearchPeople(entityResult);
                        if (personData) {
                            people.push(personData);
                        }
                    } else if (entityResult.__typename === 'Company') {
                        const companyData = this.parseSearchCompany(entityResult);
                        if (companyData) {
                            companies.push(companyData);
                        }
                    }
                });
            });

            return { people, companies, status: true };
        } catch (error) {
            console.error('JSON解析错误:', error);
            return { people: [], companies: [], status: false };
        }
    }

    /**
     * 解析HTML DOM结构 - 综合搜索页
     * @param {Document|Element} htmlDoc - HTML文档或元素
     * @returns {Object} 解析结果 {people: [], companies: [], status: boolean}
     */
    searchAllPageParserHtmlAll(htmlDoc) {
        const utils = window.ParseUtils || { getElementsByXPath, getTextByXPath, cleanText };
        const people = [];
        const companies = [];

        try {
            // 查找人物搜索结果
            const personElements = utils.getElementsByXPath('.//div[contains(@class, "entity-result")]', htmlDoc);

            personElements.forEach(element => {
                const nameElement = utils.getElementsByXPath('.//span[contains(@class, "entity-result__title")]//a', element)[0];
                const profileElement = utils.getElementsByXPath('.//p[contains(@class, "entity-result__summary")]', element)[0];
                const avatarElement = utils.getElementsByXPath('.//img[contains(@class, "entity-result__image")]', element)[0];

                if (nameElement) {
                    const humanName = utils.cleanText(nameElement.textContent || '');
                    const linkedinUrl = nameElement.href || '';
                    const linkedinUsername = utils.extractLinkedInId(linkedinUrl);
                    const humanProfiles = profileElement ? utils.cleanText(profileElement.textContent || '') : '';
                    const humanAvatar = avatarElement ? avatarElement.src || '' : '';

                    if (humanName && linkedinUsername) {
                        const personData = new (window.ParseModels?.ParseSearchAllPeople || ParseSearchAllPeople)({
                            linkedin_username: linkedinUsername,
                            linkedin_url: linkedinUrl,
                            human_name: humanName,
                            human_profiles: humanProfiles,
                            human_avatars: JSON.stringify([{ url: humanAvatar }])
                        });
                        people.push(personData);
                    }
                }
            });

            return { people, companies, status: people.length > 0 };
        } catch (error) {
            console.error('HTML解析错误:', error);
            return { people, companies, status: false };
        }
    }

    /**
     * 主解析方法
     * @param {string} htmlStr - HTML字符串
     * @param {string} fromType - 解析类型 ('html', 'json', 'html_all', 'html_people', 'html_company')
     * @returns {Object} 解析结果 {people: [], companies: []}
     */
    searchAllPageParser(htmlStr, fromType = 'html') {
        const utils = window.ParseUtils || { customUnescape };
        const resData = { people: [], companies: [] };

        if (!htmlStr) {
            return resData;
        }

        if (fromType === 'json') {
            // 直接解析JSON数据
            const result = this.searchAllPageParserJson(htmlStr);
            if (result.status) {
                return { people: result.people, companies: result.companies };
            }
        } else {
            // 解析HTML
            htmlStr = utils.customUnescape(htmlStr);
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(htmlStr, 'text/html');

            if (fromType === 'html') {
                // 通过HTML中的JSON解析
                const codeElements = htmlDoc.querySelectorAll('code');
                for (const codeElement of codeElements) {
                    const codeStr = codeElement.textContent || '';
                    const parseData = this.searchAllPageParserJson(codeStr);
                    if (parseData.status) {
                        return { people: parseData.people, companies: parseData.companies };
                    }
                }
            }

            if (fromType === 'html_all' || fromType === 'html_people') {
                // 通过HTML DOM结构解析
                const parseData = this.searchAllPageParserHtmlAll(htmlDoc);
                if (parseData.status) {
                    return { people: parseData.people, companies: parseData.companies };
                }
            }
        }

        return resData;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SearchAllParse };
} else {
    window.SearchAllParse = SearchAllParse;
}
