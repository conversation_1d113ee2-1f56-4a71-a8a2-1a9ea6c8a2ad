/**
 * Node.js环境下的LinkedIn解析器测试脚本
 * 
 * 使用方法:
 * 1. 安装依赖: npm install jsdom
 * 2. 运行: node test_node.js
 */

const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

// 创建一个模拟的浏览器环境
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
global.window = dom.window;
global.document = dom.window.document;
global.DOMParser = dom.window.DOMParser;
global.XMLSerializer = dom.window.XMLSerializer;
global.Node = dom.window.Node;
global.NodeFilter = dom.window.NodeFilter;
global.XPathResult = dom.window.XPathResult;

// 加载解析器文件
const ParseUtils = require('./parse_utils.js');
const ParseModels = require('./parse_models.js');
const { SearchAllParse } = require('./parse_search_page.js');
const { NotLoginHumanInfoParse, LoginHumanParse } = require('./parse_human_info_page.js');
const { NotLoginCompanyParse, LoginCompanyParse } = require('./parse_company_info_page.js');
const { LinkParser, parseLinkedInPage } = require('./parse.js');

// 创建测试数据目录
const TEST_DIR = path.join(__dirname, 'test_data');
if (!fs.existsSync(TEST_DIR)) {
    fs.mkdirSync(TEST_DIR);
}

/**
 * 保存测试HTML文件
 * @param {string} filename - 文件名
 * @param {string} html - HTML内容
 */
function saveTestHtml(filename, html) {
    const filePath = path.join(TEST_DIR, filename);
    fs.writeFileSync(filePath, html, 'utf8');
    console.log(`测试HTML已保存到: ${filePath}`);
}

/**
 * 加载测试HTML文件
 * @param {string} filename - 文件名
 * @returns {string} HTML内容
 */
function loadTestHtml(filename) {
    const filePath = path.join(TEST_DIR, filename);
    if (fs.existsSync(filePath)) {
        return fs.readFileSync(filePath, 'utf8');
    }
    return null;
}

/**
 * 测试搜索页解析
 */
function testSearchParser() {
    console.log('\n===== 测试搜索页解析 =====');
    
    // 创建测试HTML
    const searchHtml = `
        <html>
            <body>
                <div class="entity-result">
                    <span class="entity-result__title">
                        <a href="https://www.linkedin.com/in/test-user">Test User</a>
                    </span>
                    <p class="entity-result__summary">Software Engineer</p>
                </div>
            </body>
        </html>
    `;
    
    // 保存测试HTML
    saveTestHtml('search_test.html', searchHtml);
    
    // 解析测试HTML
    const parser = new SearchAllParse();
    const result = parser.searchAllPageParser(searchHtml, 'html_all');
    
    console.log('解析结果:', JSON.stringify(result, null, 2));
}

/**
 * 测试人物页解析
 */
function testPersonParser() {
    console.log('\n===== 测试人物页解析 =====');
    
    // 创建测试HTML
    const personHtml = `
        <html>
            <body>
                <h1 class="text-heading-xlarge">John Doe</h1>
                <div class="text-body-medium">Software Engineer at Example Inc.</div>
                <script type="application/ld+json">
                {
                    "@graph": [
                        {
                            "@type": "Person",
                            "name": "John Doe",
                            "image": {
                                "contentUrl": "https://example.com/avatar.jpg"
                            },
                            "address": {
                                "addressCountry": "US"
                            }
                        }
                    ]
                }
                </script>
            </body>
        </html>
    `;
    
    // 保存测试HTML
    saveTestHtml('person_test.html', personHtml);
    
    // 解析测试HTML
    const parser = new NotLoginHumanInfoParse();
    const result = parser.parseData(personHtml);
    
    console.log('解析结果:', JSON.stringify(result, null, 2));
}

/**
 * 测试公司页解析
 */
function testCompanyParser() {
    console.log('\n===== 测试公司页解析 =====');
    
    // 创建测试HTML
    const companyHtml = `
        <html>
            <body>
                <script type="application/ld+json">
                {
                    "@graph": [
                        {
                            "name": "Example Inc.",
                            "url": "https://www.linkedin.com/company/example-inc",
                            "logo": {
                                "url": "https://example.com/logo.jpg"
                            },
                            "description": "A software company"
                        },
                        {
                            "address": {
                                "addressLocality": "San Francisco",
                                "addressRegion": "CA"
                            }
                        }
                    ]
                }
                </script>
            </body>
        </html>
    `;
    
    // 保存测试HTML
    saveTestHtml('company_test.html', companyHtml);
    
    // 解析测试HTML
    const parser = new NotLoginCompanyParse();
    const result = parser.parseData(companyHtml);
    
    console.log('解析结果:', JSON.stringify(result, null, 2));
}

/**
 * 测试主解析入口
 */
function testMainParser() {
    console.log('\n===== 测试主解析入口 =====');
    
    // 加载测试HTML
    const searchHtml = loadTestHtml('search_test.html');
    const personHtml = loadTestHtml('person_test.html');
    const companyHtml = loadTestHtml('company_test.html');
    
    if (!searchHtml || !personHtml || !companyHtml) {
        console.error('测试HTML文件不存在，请先运行单独的测试函数');
        return;
    }
    
    // 创建解析器实例
    const parser = new LinkParser();
    
    // 测试搜索页解析
    console.log('\n>> 测试搜索页解析:');
    const searchResult = parser.parseByUrl(
        'https://www.linkedin.com/search/results/people/',
        searchHtml
    );
    console.log(JSON.stringify(searchResult, null, 2));
    
    // 测试人物页解析
    console.log('\n>> 测试人物页解析:');
    const personResult = parser.parseByUrl(
        'https://www.linkedin.com/in/test-user',
        personHtml
    );
    console.log(JSON.stringify(personResult, null, 2));
    
    // 测试公司页解析
    console.log('\n>> 测试公司页解析:');
    const companyResult = parser.parseByUrl(
        'https://www.linkedin.com/company/example-inc',
        companyHtml
    );
    console.log(JSON.stringify(companyResult, null, 2));
}

/**
 * 运行所有测试
 */
function runAllTests() {
    console.log('===== LinkedIn解析器测试开始 =====');
    
    testSearchParser();
    testPersonParser();
    testCompanyParser();
    testMainParser();
    
    console.log('\n===== LinkedIn解析器测试完成 =====');
}

// 执行测试
runAllTests();
