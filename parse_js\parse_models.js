/**
 * 解析数据模型 - JavaScript版本
 * 对应Python的link/parse_models.py
 */

/**
 * 基础模型类
 */
class BaseModel {
    constructor(data = {}) {
        Object.assign(this, data);
    }
    
    /**
     * 转换为普通对象
     */
    toObject() {
        return { ...this };
    }
    
    /**
     * 转换为JSON字符串
     */
    toJSON() {
        return JSON.stringify(this.toObject());
    }
}

/**
 * 搜索综合页 人物 解析模型
 */
class ParseSearchAllPeople extends BaseModel {
    constructor(data = {}) {
        super();
        this.linkedin_id = data.linkedin_id || '';  // 领英人物 ID
        this.linkedin_username = data.linkedin_username || '';  // 领英人物 链接ID
        this.linkedin_url = data.linkedin_url || '';  // 领英人物 链接
        this.human_name = data.human_name || '';  // 领英人物 名称
        this.human_avatars = data.human_avatars || '';  // 领英人物 头像列表json
        this.human_profiles = data.human_profiles || '';  // 领英人物 简介
        this.job_company_id = data.job_company_id || '';  // 领英公司 链接ID
        this.job_company_linkedin_id = data.job_company_linkedin_id || '';  // 领英公司 ID
        this.job_company_linkedin_url = data.job_company_linkedin_url || '';  // 领英公司 链接
        this.job_company_name = data.job_company_name || '';  // 领英公司 名称
        this.job_title = data.job_title || '';  // 领英人物 职位
        this.job_location = data.job_location || '';  // 领英人物 工作地点
        this.mutual_connections_count = data.mutual_connections_count || '';  // 领英人物 共同联系人数量
        this.is_premium = data.is_premium || false;  // 是否为高级会员
    }
}

/**
 * 搜索综合页 公司 解析模型
 */
class ParseSearchAllCompany extends BaseModel {
    constructor(data = {}) {
        super();
        this.job_company_id = data.job_company_id || '';  // 领英公司 链接ID
        this.job_company_linkedin_id = data.job_company_linkedin_id || '';  // 领英公司 ID
        this.job_company_linkedin_url = data.job_company_linkedin_url || '';  // 领英公司 链接
        this.job_company_name = data.job_company_name || '';  // 领英公司 名称
        this.company_logos = data.company_logos || '';  // 领英公司 头像列表json
        this.company_profiles = data.company_profiles || '';  // 领英公司 简介
        this.job_search_url = data.job_search_url || '';  // 领英公司 职位搜索链接
        this.job_num = data.job_num || '';  // 领英公司 职位数量
        this.followers = data.followers || '';  // 领英公司 关注者数量
        this.company_size = data.company_size || '';  // 领英公司 规模
        this.company_industry = data.company_industry || '';  // 领英公司 行业
        this.company_location = data.company_location || '';  // 领英公司 位置
    }
}

/**
 * 人物详情页 解析模型
 */
class ParseHumanInfo extends BaseModel {
    constructor(data = {}) {
        super();
        this.linkedin_id = data.linkedin_id || '';
        this.linkedin_username = data.linkedin_username || '';
        this.linkedin_url = data.linkedin_url || '';
        this.human_name = data.human_name || '';
        this.human_avatar = data.human_avatar || '';
        this.human_profiles = data.human_profiles || '';
        this.job_title = data.job_title || '';
        this.job_company_name = data.job_company_name || '';
        this.job_location = data.job_location || '';
        this.country_code = data.country_code || '';
        this.work_experience = data.work_experience || [];
        this.educational_experience = data.educational_experience || [];
        this.language_ability = data.language_ability || [];
        this.skills = data.skills || [];
        this.connections_count = data.connections_count || '';
    }
}

/**
 * 公司详情页 解析模型
 */
class ParseCompanyInfo extends BaseModel {
    constructor(data = {}) {
        super();
        this.job_company_id = data.job_company_id || '';
        this.job_company_linkedin_id = data.job_company_linkedin_id || '';
        this.job_company_linkedin_url = data.job_company_linkedin_url || '';
        this.job_company_name = data.job_company_name || '';
        this.company_logo = data.company_logo || '';
        this.company_profiles = data.company_profiles || '';
        this.company_website = data.company_website || '';
        this.company_industry = data.company_industry || '';
        this.company_size = data.company_size || '';
        this.company_location = data.company_location || '';
        this.company_founded = data.company_founded || '';
        this.company_specialties = data.company_specialties || '';
        this.followers = data.followers || '';
        this.employees = data.employees || [];
        this.overview = data.overview || {};
        this.job_url = data.job_url || '';
    }
}

/**
 * 工作经历模型
 */
class WorkExperience extends BaseModel {
    constructor(data = {}) {
        super();
        this.company_name = data.company_name || '';
        this.job_title = data.job_title || '';
        this.duration = data.duration || '';
        this.location = data.location || '';
        this.description = data.description || '';
        this.company_logo = data.company_logo || '';
    }
}

/**
 * 教育经历模型
 */
class EducationalExperience extends BaseModel {
    constructor(data = {}) {
        super();
        this.school_name = data.school_name || '';
        this.degree = data.degree || '';
        this.field_of_study = data.field_of_study || '';
        this.duration = data.duration || '';
        this.description = data.description || '';
        this.school_logo = data.school_logo || '';
    }
}

/**
 * 语言能力模型
 */
class LanguageAbility extends BaseModel {
    constructor(data = {}) {
        super();
        this.language = data.language || '';
        this.proficiency = data.proficiency || '';
    }
}

/**
 * 员工信息模型
 */
class EmployeeInfo extends BaseModel {
    constructor(data = {}) {
        super();
        this.name = data.name || '';
        this.title = data.title || '';
        this.linkedin_url = data.linkedin_url || '';
        this.avatar = data.avatar || '';
    }
}

// 导出所有模型
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        BaseModel,
        ParseSearchAllPeople,
        ParseSearchAllCompany,
        ParseHumanInfo,
        ParseCompanyInfo,
        WorkExperience,
        EducationalExperience,
        LanguageAbility,
        EmployeeInfo
    };
} else {
    // 浏览器环境
    window.ParseModels = {
        BaseModel,
        ParseSearchAllPeople,
        ParseSearchAllCompany,
        ParseHumanInfo,
        ParseCompanyInfo,
        WorkExperience,
        EducationalExperience,
        LanguageAbility,
        EmployeeInfo
    };
}
